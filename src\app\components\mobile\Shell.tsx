"use client";

import { useMemo, useState } from "react";
import Sidebar from "./sidebar/SideBar";
import Header from "./header/Header";
import { usePathname } from "next/navigation";

const TOOLS = ["trim", "convert", "crop", "rotation"];

export default function Shell({ children }: { children: React.ReactNode }) {
  const [open, setOpen] = useState(false);
  const pathname = usePathname();

  const useToolFromPath = (pathname: string) => {
    const segs = pathname.split("/").filter(Boolean); // ["mobile","trim","1"]
    return segs[1] ?? ""; // "trim"
  };

  const showHeader = !TOOLS.includes(useToolFromPath(pathname));

  return (
    <div className="min-h-screen w-screen">
      {/* Sidebar */}
      <Sidebar open={open} onClose={() => setOpen(false)} />

      {/* Content wrapper
          - Khi sidebar mở, nội dung dịch padding-left 72 (desktop) mượt
       */}
      <div
        className={[
          "transition-[padding] duration-300 ease-out ",
          open ? "lg:pl-72" : "lg:pl-0",
        ].join(" ")}
      >
        {showHeader && <Header onToggleSidebar={() => setOpen(true)} />}
        <main className="w-screen">{children}</main>
      </div>
    </div>
  );
}