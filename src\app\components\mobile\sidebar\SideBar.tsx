"use client";

import Link from "next/link";
import Image from "next/image";
import {
  StarOutlined,
  MessageOutlined,
  LoginOutlined,
  PlayCircleOutlined,
  SafetyCertificateOutlined,
  SettingOutlined,
  CloseOutlined,
  CrownOutlined,
} from "@ant-design/icons";
import { useEffect, useState } from "react";
import PremiumModal from "../premium/PremiumModal";

type Props = { open: boolean; onClose: () => void };

const menuItems = [
  { href: "#", label: "Rate", icon: <StarOutlined /> },
  { href: "#", label: "Give feedback", icon: <MessageOutlined /> },
  { href: "#", label: "Sign in", icon: <LoginOutlined /> },
  { href: "#", label: "Video tutorials", icon: <PlayCircleOutlined /> },
  { href: "#", label: "Privacy Option", icon: <SafetyCertificateOutlined /> },
  { href: "#", label: "Settings", icon: <SettingOutlined /> },
];

export default function SideBar({ open, onClose }: Props) {
  const [premiumModalOpen, setPremiumModalOpen] = useState(false);

  useEffect(() => {
    const onKey = (e: KeyboardEvent) => e.key === "Escape" && onClose();
    window.addEventListener("keydown", onKey);
    return () => window.removeEventListener("keydown", onKey);
  }, [onClose]);

  return (
    <>
      {/* Overlay */}
      <div
        className={[
          "fixed inset-0 z-40 bg-black/40 backdrop-blur-sm transition-opacity duration-300 ease-out lg:hidden",
          open
            ? "opacity-100 pointer-events-auto"
            : "opacity-0 pointer-events-none",
        ].join(" ")}
        onClick={onClose}
      />

      {/* Panel */}
      <aside
        className={[
          "fixed top-0 left-0 z-50 h-screen w-90",
          "bg-[#2C2C2C] text-white shadow-xl",
          open ? "translate-x-0" : "-translate-x-full",
        ].join(" ")}
        aria-hidden={!open}
      >
        {/* Header với Close button */}

        {/* Logo Section */}
        <div className="px-6 py-8 flex flex-col items-center text-center">
          {/* App Icon */}
          <div className="w-64 h-64 flex items-center justify-center relative overflow-hidden">
            {/* Logo Section */}
            <div className="flex flex-col items-center text-center px-6 py-8">
              <Image
                src="/assets/svg/fast-logo.svg"
                alt="Fast Converter Logo"
                width={200}
                height={200}
                className="rounded-xl mb-6"        
              />
            </div>

            {/* Arrow decorations */}
            {/* <div className="absolute bottom-3 right-3 text-white/80">
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="currentColor"
              >
                <path d="M8 2L14 8L8 14L2 8L8 2Z" />
              </svg>
            </div> */}
          </div>

          {/* App Title */}
          <h1 className="text-white font-medium text-[25px] leading-[100%] tracking-[0]">
            Fast Converter For Video
          </h1>
          <p className="mt-4 font-normal text-[18px] leading-[100%] tracking-[0.5em] mb-7">
            FREE VERSION
          </p>

          {/* Premium Button */}
          <button
            onClick={() => setPremiumModalOpen(true)}
            className="w-[300px] h-[63px] bg-[#4e2eaf] mb-2 rounded-[8px] opacity-100 p-[10px] gap-[10px] font-medium text-[22px] leading-[100%] tracking-[0.2em] flex items-center justify-start text-white"
          >
            <span className="text-lg mr-4 ml-2 text-[26px] bg-gradient-to-r from-[#FFE946] to-[#F9B805] text-transparent bg-clip-text">
              <Image src="/assets/svg/Vector.svg" alt="Premium" width={26} height={26} />
            </span>
            BUY PREMIUM
          </button>

          {/* Subtitle */}
          <p className=" font-normal text-[18px] leading-[20px] tracking-[0] text-center">
            Sign in to track your
            <br />
            compression-saving statistics
          </p>
        </div>

        {/* Menu Items */}
        <nav className="px-4 space-y-1">
          {menuItems.map((item) => (
            <Link
              key={item.label}
              href={item.href}
              className="flex items-center gap-4 px-4 py-3 text-white hover:bg-white/5 transition-colors duration-200 text-sm"
              onClick={onClose}
            >
              <span className="text-lg w-6 flex justify-center">
                {item.icon}
              </span>
              <span className="font-normal text-[22px] leading-[100%] tracking-[0]">
                {item.label}
              </span>
            </Link>
          ))}
          {/* Version */}
          <div className=" font-light text-[22px] leading-[100%] tracking-[0] ml-4 mt-10">
            Version:{" "}
            <span className="text-[#FF0000] font-light text-[22px] leading-[100%] tracking-[0]">
              01
            </span>
          </div>
        </nav>
      </aside>

      {/* Premium Modal */}
      <PremiumModal
        open={premiumModalOpen}
        onClose={() => setPremiumModalOpen(false)}
      />
    </>
  );
}
