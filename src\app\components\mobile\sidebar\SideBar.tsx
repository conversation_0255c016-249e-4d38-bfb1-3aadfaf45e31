"use client";

import Link from "next/link";
import Image from "next/image";
import {
  StarOutlined,
  MessageOutlined,
  LoginOutlined,
  PlayCircleOutlined,
  SafetyCertificateOutlined,
  SettingOutlined,
  CloseOutlined,
  CrownOutlined,
} from "@ant-design/icons";
import { useEffect, useState } from "react";
import PremiumModal from "@/components/Shared/popup/PremiumModal";

type Props = { open: boolean; onClose: () => void };

const menuItems = [
  { href: "#", label: "Rate", icon: <Image src="/assets/svg/starr.svg" alt="Rate" width={24} height={24} className="w15 h15 max-[376px]:w10 max-[376px]:h10" /> },
  { href: "#", label: "Give feedback", icon: <Image src="/assets/svg/fback.svg" alt="Give feedback" width={24} height={24} className="w15 h15 max-[376px]:w10 max-[376px]:h10" /> },
  { href: "#", label: "Sign in", icon: <Image src="/assets/svg/login.svg" alt="Sign in" width={24} height={24} className="w15 h15 max-[376px]:w10 max-[376px]:h10" /> },
  { href: "#", label: "Video tutorials", icon: <Image src="/assets/svg/youtube.svg" alt="Video tutorials" width={24} height={24} className="w15 h15 max-[376px]:w10 max-[376px]:h10" /> },
  { href: "#", label: "Privacy Option", icon: <Image src="/assets/svg/privacy.svg" alt="Privacy Option" width={24} height={24} className="w15 h15 max-[376px]:w10 max-[376px]:h10" /> },
  { href: "#", label: "Settings", icon: <Image src="/assets/svg/setting.svg" alt="Settings" width={24} height={24} className="w15 h15 max-[376px]:w10 max-[376px]:h10" /> },
];

export default function SideBar({ open, onClose }: Props) {
  const [premiumModalOpen, setPremiumModalOpen] = useState(false);

  useEffect(() => {
    const onKey = (e: KeyboardEvent) => e.key === "Escape" && onClose();
    window.addEventListener("keydown", onKey);
    return () => window.removeEventListener("keydown", onKey);
  }, [onClose]);

  return (
    <>
      {/* Overlay */}
      <div
        className={[
          "fixed inset-0 z-40 bg-black/40 backdrop-blur-sm transition-opacity duration-300 ease-out lg:hidden",
          open
            ? "opacity-100 pointer-events-auto"
            : "opacity-0 pointer-events-none",
        ].join(" ")}
        onClick={onClose}
      />

      {/* Panel */}
      <aside
        className={[
          "fixed top-0 left-0 z-50 h-screen w-[85%] ",
          "bg-[#2C2C2C] text-white shadow-xl overflow-y-auto scrollbar-hide",
          open ? "translate-x-0" : "-translate-x-full",
        ].join(" ")}
        aria-hidden={!open}
      >
        {/* Header với Close button */}

        {/* Logo Section */}
        <div className="flex flex-col items-center text-center  max-[376px]:py-2 max-[376px]:px-2 sm:px-4 sm:py-6">
          {/* App Icon */}
          <div className="w-full flex items-center justify-center relative mb-6">
            {/* Logo Section */}
            <div className="flex flex-col items-center text-center">
              <Image
                src="/assets/svg/fast-logo.svg"
                alt="Fast Converter Logo"
                width={100}
                height={100}
                className="rounded-xl w-50 h-50 max-[376px]:w-28 max-[376px]:h-28 mt-9 max-[376px]:mt-4"
              />
            </div>
            <button
              onClick={onClose}
              aria-label="Close sidebar"
              className="absolute top-3 right-3 grid place-items-center size-9 rounded-xl hover:bg-white/10 transition"
            >
              <CloseOutlined className="text-lg max-[376px]:text-sm" />
            </button>
          </div>

          {/* App Title */}
          <h1 className="text-2xl max-[376px]:text-xl">
            Fast Converter For Video
          </h1>
          <p className="mb-4 font-normal text-lg max-[376px]:text-sm">
            FREE VERSION
          </p>

          {/* Premium Button */}
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setPremiumModalOpen(true);
              onClose(); // Close sidebar when opening premium modal
            }}
            className="max-content bg-primary mb-4 rounded-md px-6 py-3 gap-2 font-medium text-xl leading-[100%] tracking-[0.2em] flex items-center justify-center text-white max-[376px]:text-sm max-[376px]:py-2 max-[376px]:px-2 max-[376px]:gap-1 max-[376px]:mb-2 max-[376px]:rounded-sm max-[376px]:w-full "
          >
            <span className="text-lg mr-2 text-2xl bg-gradient-to-r from-[#FFE946] to-[#F9B805] text-transparent bg-clip-text max-[376px]:text-sm max-[376px]:mr-1 max-[376px]:w-5 max-[376px]:h-5 max-[376px]:bg-transparent max-[376px]:flex max-[376px]:justify-center max-[376px]:items-center ">
              <Image src="/assets/svg/Vector.svg" alt="Premium" width={24} height={24} />
            </span>
            BUY PREMIUM
          </button>

          {/* Subtitle */}
          <p className="font-normal text-lg leading-6 text-center mb-6 max-[376px]:text-sm max-[376px]:mb-2 max-[376px]:text-center">
            Sign in to track your
            <br />
            compression-saving statistics
          </p>
        </div>

        {/* Menu Items */}
        <nav className="px-4 space-y-1 max-[376px]:px-2 max-[376px]:space-y-0.5">
          {menuItems.map((item) => (
            <Link
              key={item.label}
              href={item.href}
              className="flex items-center gap-4 px-8 py-3 text-white "
              onClick={onClose}
            >
              <span className="text-lg w-6 flex justify-center max-[376px]:w-5 max-[376px]:h-5 ">
                {item.icon}
              </span>
              <span className="font-normal text-xl leading-6 max-[376px]:text-sm max-[376px]:leading-5">
                {item.label}
              </span>
            </Link>
          ))}
          {/* Version */}
          <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 font-light text-lg leading-6 text-center max-[376px]:bottom-2 max-[376px]:text-sm max-[376px]:leading-5 max-[376px]:text-center max-[376px]:bottom-4 text-foreground-4">
            Version:{" "}
            <span className=" font-light text-lg leading-6 max-[376px]:text-sm max-[376px]:leading-5 ">
              01
            </span>
          </div>
        </nav>
      </aside>

      {/* Premium Modal */}
      <PremiumModal
        open={premiumModalOpen}
        onClose={() => setPremiumModalOpen(false)}
      />
    </>
  );
}
