"use client";

import Image from "next/image";
import { useEffect, useState } from "react";

type SavingOverlayProps = {
  videoThumbnail: string;
  onCancel: () => void;
  onComplete?: () => void;
};

export default function SavingOverlay({ videoThumbnail, onCancel, onComplete }: SavingOverlayProps) {
  const [progress, setProgress] = useState(45); // Start at 45% for design
  const [isLoading, setIsLoading] = useState(true);

  // Auto progress for design demo
  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          onComplete?.();
          return 100;
        }
        return prev + Math.random() * 8; // Slower progress for design
      });
    }, 500); // Slower interval for design

    return () => clearInterval(interval);
  }, [onComplete]);

  return (
    <div className="fixed inset-0 bg-black z-50 flex flex-col items-center justify-center p-6">
      {/* Header */}
      <h1 className="text-2xl font-medium text-white text-center mb-8">
        Saving your video...
      </h1>

      {/* Video Preview Container */}
      <div className="relative w-full max-w-sm aspect-video rounded-xl overflow-hidden mb-8">
        {/* Video Thumbnail */}
        <div 
          className="w-full h-full bg-cover bg-center"
          style={{ backgroundImage: `url('${videoThumbnail}')` }}
        />
        
        {/* Loading Spinner Overlay */}
        <div className="absolute inset-0 bg-black/40 flex flex-col items-center justify-center">
          {/* Circular Loading Spinner */}
          <div className="relative w-16 h-16 mb-4">
            <div className="absolute inset-0 border-4 border-white/20 rounded-full"></div>
            <div 
              className="absolute inset-0 border-4 border-white rounded-full"
              style={{
                clipPath: `polygon(0 0, 100% 0, 100% 100%, 0 100%)`,
                transform: `rotate(${progress * 3.6}deg)`
              }}
            ></div>
            {/* Center dot */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full"></div>
            </div>
          </div>
          
          {/* Progress Percentage */}
          <div className="text-white text-2xl font-bold mb-4">
            {Math.round(progress)}%
          </div>
          
          {/* Progress Bar */}
          <div className="w-full max-w-xs">
            <div className="w-full h-2 bg-white/20 rounded-full overflow-hidden">
              <div 
                className="h-full bg-primary rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Instruction Text */}
      <p className="text-white/80 text-center text-sm leading-relaxed mb-8 max-w-sm">
        Please do not close Fast video convert or lock the screen while exporting video
      </p>

      {/* Cancel Button */}
      <button
        onClick={onCancel}
        className="w-full max-w-sm bg-primary hover:bg-primary-2 text-white text-center py-4 font-medium text-lg rounded-xl transition-colors"
      >
        HỦY
      </button>
    </div>
  );
}
