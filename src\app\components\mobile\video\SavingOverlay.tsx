"use client";


import Image from "next/image";
import { useEffect, useState } from "react";
import SuccessModal from "./SuccessModal";

type SavingOverlayProps = {
  videoThumbnail: string;
  onCancel: () => void;
  onComplete?: () => void;
};

export default function SavingOverlay({ videoThumbnail, onCancel, onComplete }: SavingOverlayProps) {
  const [progress, setProgress] = useState(45); // Start at 45% for design
  const [isLoading, setIsLoading] = useState(true);
  const [showSuccess, setShowSuccess] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          // Show success modal after completion
          setTimeout(() => {
            setShowSuccess(true);
          }, 500);
          return 100;
        }
        return prev + Math.random() * 8; // Slower progress for design
      });
    }, 500); // Slower interval for design

    return () => clearInterval(interval);
  }, [onComplete]);

  const handleContinue = () => {
    setShowSuccess(false);
    onComplete?.();
  };

  return (
    <div className="fixed inset-0 bg-black z-50 flex flex-col items-center justify-center mb-6 max-[376px]:mb-4 px-2 h-screen ">
      {/* Header */}
      <h1 className="text-lg font-medium text-white text-center mb-8 max-[376px]:text-base mb-14 max-[376px]:mb-10">
        Saving your video...
      </h1>
      <div className="relative overflow-hidden mb-8 border-3 border-white border-solid border rounded-lg w-[260px] h-[260px]">
        <Image src={videoThumbnail} alt="Video Thumbnail" fill className="object-cover" />
        <div className="bg-black/20 w-full h-full absolute top-0 left-0 flex flex-col items-center justify-center px-2">
          <Image src="/assets/svg/Loading.svg" alt="Loading" width={50} height={50} className="max-[376px]:w-10 max-[376px]:h-10 mb-5" />
          
          <div className="w-full max-w-xs absolute bottom-4  text-center px-2">
          <p className="text-white text-3xl font-medium mb-2">
            {Math.round(progress)}%
          </p>
            <div className="w-full h-2 bg-white/20 rounded-full overflow-hidden">
              <div 
                className="h-full bg-primary rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        </div>
      </div>

    

      {/* Instruction Text */}
      <p className="text-center  leading-relaxed mb-8 max-w-sm max-[376px]:text-sm w-[303px] max-[376px]:w-[250px]">
        Please do not close Fast video convert or lock the screen while exporting video
      </p>

      {/* Cancel Button */}
      <button
        onClick={onCancel}
        className="text-2xl w-full font-medium bg-primary text-center py-4 font-medium text-lg rounded-xl transition-colors  max-[376px]:text-base"
      >
        HỦY
      </button>

      {/* Success Modal */}
      <SuccessModal 
        isOpen={showSuccess}
        title="You have successfully cropped the video"
        onContinue={handleContinue}
      />
    </div>
  );
}
