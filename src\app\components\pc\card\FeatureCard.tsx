const FeatureCard = ({
  icon,
  title,
  desc,
}: {
  icon: React.ReactNode;
  title: string;
  desc: string;
}) => (
  <div className="rounded-xl bg-[#faf7ff] p-6 shadow-[0_1px_0_#eee] flex justify-center items-center flex-col gap-2">
    <div className="h-12 w-12 rounded-xl bg-white flex items-center justify-center">
      <span className="text-purple-600 text-2xl">{icon}</span>
    </div>
    <p className="text-2xl font-semibold text-gray-900 text-center">{title}</p>
    <p className="text-base font-thin text-center">{desc}</p>
  </div>
);

export default FeatureCard;
