"use client";

import Link from "next/link";
import React from "react";

/** Icon caret xuống */
const CaretDown: React.FC<{ className?: string }> = ({ className }) => (
  <svg viewBox="0 0 16 16" className={className} aria-hidden="true">
    <path
      d="M4 6l4 4 4-4"
      fill="none"
      stroke="currentColor"
      strokeWidth="1.6"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

// Kiểu dữ liệu cho menu
type MenuItem = {
  label: string;
  href?: string;
  children?: MenuItem[];
};

// Định nghĩa list menu
const menuItems: MenuItem[] = [
  {
    label: "Convert",
    children: [
      { label: "PDF to Word", href: "/convert/pdf-to-word" },
      { label: "Word to PDF", href: "/convert/word-to-pdf" },
    ],
  },
  { label: "Compress", href: "/compress" },
  { label: "Tools", href: "/tools" },
  { label: "Pricing", href: "/pricing" },
  { label: "Blog", href: "/blog" },
];

const HeaderMenu: React.FC = () => {
  return (
    <nav className="flex items-center gap-8 text-gray-700 font-medium">
      {menuItems.map((item) =>
        item.children ? (
          <div key={item.label} className="relative group">
            {/* Nút có dropdown */}
            <button className="flex items-center gap-1.5 hover:text-primary focus:text-primary outline-none !text-18 !font-light">
              {item.label}
              <CaretDown className="mt-0.5 h-3.5 w-3.5 transition-transform group-hover:rotate-180" />
            </button>

            {/* Dropdown */}
            <div
              className="invisible group-hover:visible opacity-0 group-hover:opacity-100
                         transition-all duration-150
                         absolute left-0 top-full mt-2 min-w-44
                         rounded-xl border border-gray-200 bg-white shadow-lg"
              role="menu"
            >
              {item.children.map((child) => (
                <Link
                  key={child.label}
                  href={child.href!}
                  role="menuitem"
                  className="block px-4 py-2 hover:bg-gray-50 hover:text-primary !text-18 !font-light"
                >
                  {child.label}
                </Link>
              ))}
            </div>
          </div>
        ) : (
          <Link
            key={item.label}
            href={item.href!}
            className="hover:text-primary !text-lg !font-light"
          >
            {item.label}
          </Link>
        )
      )}
    </nav>
  );
};

export default HeaderMenu;
