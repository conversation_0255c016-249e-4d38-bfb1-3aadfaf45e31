/* CSS Module chỉ cho hiệu <PERSON>ng, animation, pattern phức tạp mà <PERSON>wind không có */

/* Background patterns cho từng rarity */
.backgroundPattern {
  opacity: 0.1;
  background-size: 20px 20px;
  background-repeat: repeat;
}

.rarity-common .backgroundPattern {
  background-image: radial-gradient(circle, #6b7280 1px, transparent 1px);
}

.rarity-rare .backgroundPattern {
  background-image: linear-gradient(45deg, #3b82f6 25%, transparent 25%),
    linear-gradient(-45deg, #3b82f6 25%, transparent 25%);
  background-size: 10px 10px;
}

.rarity-epic .backgroundPattern {
  background-image: conic-gradient(from 0deg, #8b5cf6, #a855f7, #8b5cf6);
}

.rarity-legendary .backgroundPattern {
  background-image: radial-gradient(
      circle at 25% 25%,
      #fbbf24 0%,
      transparent 50%
    ),
    radial-gradient(circle at 75% 75%, #f59e0b 0%, transparent 50%);
}

/* Active glow effect */
.activeGlow {
  box-shadow: 0 0 20px 4px rgba(34, 197, 94, 0.4),
    0 0 40px 8px rgba(34, 197, 94, 0.2);
  animation: activeGlowPulse 2s ease-in-out infinite alternate;
}

@keyframes activeGlowPulse {
  0% {
    box-shadow: 0 0 20px 4px rgba(34, 197, 94, 0.4),
      0 0 40px 8px rgba(34, 197, 94, 0.2);
  }
  100% {
    box-shadow: 0 0 30px 6px rgba(34, 197, 94, 0.6),
      0 0 60px 12px rgba(34, 197, 94, 0.3);
  }
}

/* Hover effect */
.hoverEffect {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Active pulse indicator */
.activePulse {
  animation: pulseGlow 1.5s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.7;
  }
}

/* Shine effect animation */
.shineEffect {
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 70%
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease-in-out;
}

.rarity-legendary .shineEffect {
  animation: legendaryShine 3s ease-in-out infinite;
}

@keyframes legendaryShine {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* Rarity specific glows */
.rarity-rare:hover {
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
}

.rarity-epic:hover {
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.3);
}

.rarity-legendary:hover {
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.4), 0 0 40px rgba(251, 191, 36, 0.2);
}
