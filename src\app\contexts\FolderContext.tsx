"use client";

import { createContext, useContext, useState, ReactNode } from "react";

interface FolderContextType {
  currentFolder: string | null;
  setCurrentFolder: (folderId: string | null) => void;
  folderName: string;
  setFolderName: (name: string) => void;
}

const FolderContext = createContext<FolderContextType | undefined>(undefined);

export function FolderProvider({ children }: { children: ReactNode }) {
  const [currentFolder, setCurrentFolder] = useState<string | null>(null);
  const [folderName, setFolderName] = useState<string>("");

  return (
    <FolderContext.Provider value={{ 
      currentFolder, 
      setCurrentFolder, 
      folderName, 
      setFolderName 
    }}>
      {children}
    </FolderContext.Provider>
  );
}

export function useFolder() {
  const context = useContext(FolderContext);
  if (context === undefined) {
    throw new Error("useFolder must be used within a FolderProvider");
  }
  return context;
}
