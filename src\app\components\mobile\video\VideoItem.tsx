"use client";

import Image from "next/image";

type VideoItemProps = {
  title: string;
  thumbnail: string;
  size: string;
  duration: string;
  date?: string;
};

export default function VideoItem({
  title,
  thumbnail,
  size,
  duration,
  date,
}: VideoItemProps) {
  return (
    <div className="overflow-hidden max-[376px]:w-full max-[376px]:h-full">
      {/* Thumbnail */}
      <div className="relative w-92 h-52 max-[376px]:w-full max-[376px]:h-full">
        <Image
          src={thumbnail}
          alt={title}
          width={112}
          height={112}
          className="object-cover w-92 h-52 max-[376px]:w-full max-[376px]:h-full"
        />
        {/* Duration overlay */}
        <div className="text-sm  tracking-[0] max-[376px]:text-xs max-[376px]:tracking-[0] max-[376px]:bottom-1 max-[376px]:right-1">
          <span className="absolute left-2 bottom-2">{size}</span>
          <span className="absolute right-2 bottom-2">{duration}</span>
        </div>
      </div>
    </div>
  );
}
