"use client";

import Image from "next/image";

type VideoItemProps = {
  title: string;
  thumbnail: string;
  size: string;
  duration: string;
  date?: string;
};

export default function VideoItem({
  title,
  thumbnail,
  size,
  duration,
  date,
}: VideoItemProps) {
  return (
    <div className="overflow-hidden">
      {/* Thumbnail */}
      <div className="relative aspect-video">
        <Image
          src={thumbnail}
          alt={title}
          fill
          className="object-cover w-36 h-24  rotate-0 "
        />
        {/* Duration overlay */}
        <div className="text-sm  tracking-[0]">
          <span className="absolute left-2 bottom-2">{size}</span>
          <span className="absolute right-2 bottom-2">{duration}</span>
        </div>
      </div>
    </div>
  );
}
