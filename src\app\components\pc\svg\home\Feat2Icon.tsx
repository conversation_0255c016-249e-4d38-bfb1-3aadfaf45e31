"use client";
import React from "react";

const Feat2IconSVG: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {
  return (
    <svg
      width="65"
      height="49"
      viewBox="0 0 65 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M50.0484 21.4375C50.0484 22.0432 49.8667 22.6353 49.5265 23.1389C49.1862 23.6426 48.7025 24.0351 48.1367 24.2669C47.5708 24.4987 46.9482 24.5593 46.3474 24.4412C45.7467 24.323 45.1949 24.0313 44.7618 23.603C44.3287 23.1747 44.0338 22.629 43.9143 22.035C43.7948 21.4409 43.8562 20.8251 44.0905 20.2655C44.3249 19.7059 44.7218 19.2276 45.2311 18.8911C45.7404 18.5546 46.3391 18.375 46.9516 18.375C47.7729 18.375 48.5606 18.6977 49.1413 19.272C49.7221 19.8463 50.0484 20.6253 50.0484 21.4375ZM39.7258 8.16667H29.4032C28.8557 8.16667 28.3305 8.38177 27.9434 8.76466C27.5562 9.14754 27.3387 9.66685 27.3387 10.2083C27.3387 10.7498 27.5562 11.2691 27.9434 11.652C28.3305 12.0349 28.8557 12.25 29.4032 12.25H39.7258C40.2733 12.25 40.7984 12.0349 41.1856 11.652C41.5728 11.2691 41.7903 10.7498 41.7903 10.2083C41.7903 9.66685 41.5728 9.14754 41.1856 8.76466C40.7984 8.38177 40.2733 8.16667 39.7258 8.16667ZM64.5 20.4167V28.5833C64.5 30.2078 63.8474 31.7657 62.6859 32.9144C61.5244 34.063 59.949 34.7083 58.3064 34.7083H57.6974L53.5142 46.2897C53.2278 47.083 52.7003 47.7692 52.0041 48.2545C51.3078 48.7397 50.4769 49.0001 49.6251 49H46.3426C45.4908 49.0001 44.6599 48.7397 43.9636 48.2545C43.2674 47.7692 42.7399 47.083 42.4535 46.2897L41.958 44.9167H27.171L26.6755 46.2897C26.3891 47.083 25.8616 47.7692 25.1654 48.2545C24.4691 48.7397 23.6382 49.0001 22.7864 49H19.5039C18.6521 49.0001 17.8212 48.7397 17.1249 48.2545C16.4287 47.7692 15.9012 47.083 15.6148 46.2897L12.371 37.3166C9.28498 33.8626 7.35267 29.5505 6.83806 24.9696C6.1718 25.3157 5.61385 25.8352 5.22451 26.4721C4.83518 27.1091 4.62926 27.8391 4.62903 28.5833C4.62903 29.1248 4.41152 29.6441 4.02435 30.027C3.63718 30.4099 3.11206 30.625 2.56451 30.625C2.01697 30.625 1.49185 30.4099 1.10468 30.027C0.717511 29.6441 0.5 29.1248 0.5 28.5833C0.503155 26.7626 1.12148 24.9951 2.25673 23.5617C3.39199 22.1283 4.97906 21.1111 6.7658 20.6719C7.22701 15.0454 9.81102 9.79615 14.0047 5.96658C18.1984 2.137 23.6951 0.00716771 29.4032 0H56.2419C56.7894 0 57.3146 0.215104 57.7017 0.597991C58.0889 0.980877 58.3064 1.50018 58.3064 2.04167C58.3064 2.58315 58.0889 3.10246 57.7017 3.48534C57.3146 3.86823 56.7894 4.08333 56.2419 4.08333H50.7219C54.1481 6.46059 56.8427 9.72862 58.5077 13.526C58.6187 13.7812 58.7271 14.0365 58.8303 14.2917C60.3805 14.4218 61.8244 15.1241 62.8749 16.259C63.9254 17.3938 64.5055 18.8781 64.5 20.4167ZM60.3709 20.4167C60.3709 19.8752 60.1534 19.3559 59.7663 18.973C59.3791 18.5901 58.854 18.375 58.3064 18.375H57.3619C56.9222 18.3755 56.4938 18.2371 56.1392 17.98C55.7846 17.723 55.5222 17.3606 55.3903 16.9458C54.2075 13.213 51.8493 9.95165 48.6599 7.63774C45.4705 5.32383 41.6169 4.07845 37.6613 4.08333H29.4032C25.7973 4.08315 22.2693 5.12058 19.2493 7.06912C16.2294 9.01766 13.848 11.7931 12.3955 15.057C10.9431 18.3209 10.4824 21.9323 11.0696 25.4507C11.6568 28.969 13.2666 32.2424 15.7026 34.8717C15.8888 35.072 16.0327 35.3072 16.1258 35.5633L19.5039 44.9167H22.7864L23.7722 42.1885C23.9154 41.7921 24.1789 41.4491 24.5268 41.2065C24.8746 40.9639 25.2898 40.8336 25.7155 40.8333H43.4135C43.8392 40.8336 44.2544 40.9639 44.6022 41.2065C44.9501 41.4491 45.2136 41.7921 45.3568 42.1885L46.3426 44.9167H49.6251L54.2987 31.9802C54.4418 31.5837 54.7053 31.2407 55.0532 30.9981C55.4011 30.7555 55.8162 30.6252 56.2419 30.625H58.3064C58.854 30.625 59.3791 30.4099 59.7663 30.027C60.1534 29.6441 60.3709 29.1248 60.3709 28.5833V20.4167Z"
        fill="#4E2EAF"
      />
    </svg>
  );
};

export default Feat2IconSVG;
