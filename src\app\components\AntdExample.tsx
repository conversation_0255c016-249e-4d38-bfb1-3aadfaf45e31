"use client";

import React, { useState } from 'react';
import {
  Button,
  Form,
  Input,
  Select,
  Checkbox,
  Radio,
  DatePicker,
  TimePicker,
  Switch,
  Slider,
  Rate,
  Upload,
  message,
  Modal,
  Table,
  Tag,
  Progress,
  Alert,
  Divider,
  Space,
  Row,
  Col,
  Card,
  Typography
} from 'antd';
import {
  UploadOutlined,
  UserOutlined,
  LockOutlined,
  MailOutlined,
  PhoneOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;

interface FormData {
  username: string;
  email: string;
  phone: string;
  role: string;
  notifications: boolean;
  gender: string;
  birthDate: any;
  experience: number;
  rating: number;
}

export default function AntdExample() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);

  const onFinish = async (values: FormData) => {
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    message.success('Form submitted successfully!');
    console.log('Form values:', values);
    setLoading(false);
    setVisible(true);
  };

  const tableColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: 'Tags',
      key: 'tags',
      dataIndex: 'tags',
      render: (tags: string[]) => (
        <>
          {tags.map(tag => (
            <Tag color="blue" key={tag}>
              {tag}
            </Tag>
          ))}
        </>
      ),
    },
  ];

  const tableData = [
    {
      key: '1',
      name: 'John Brown',
      age: 32,
      address: 'New York No. 1 Lake Park',
      tags: ['nice', 'developer'],
    },
    {
      key: '2',
      name: 'Jim Green',
      age: 42,
      address: 'London No. 1 Lake Park',
      tags: ['manager'],
    },
  ];

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <Title level={2} className="text-center mb-8">
        Ant Design Components Showcase
      </Title>

      <Row gutter={[24, 24]}>
        {/* Form Section */}
        <Col xs={24} lg={12}>
          <Card title="Registration Form" className="h-fit">
            <Form
              form={form}
              name="registration"
              onFinish={onFinish}
              layout="vertical"
              initialValues={{
                notifications: true,
                gender: 'male',
                experience: 3,
                rating: 4
              }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="username"
                    label="Username"
                    rules={[{ required: true, message: 'Please input username!' }]}
                  >
                    <Input prefix={<UserOutlined />} placeholder="Username" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="email"
                    label="Email"
                    rules={[
                      { required: true, message: 'Please input email!' },
                      { type: 'email', message: 'Please enter valid email!' }
                    ]}
                  >
                    <Input prefix={<MailOutlined />} placeholder="Email" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="phone"
                    label="Phone"
                    rules={[{ required: true, message: 'Please input phone!' }]}
                  >
                    <Input prefix={<PhoneOutlined />} placeholder="Phone" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="role"
                    label="Role"
                    rules={[{ required: true, message: 'Please select role!' }]}
                  >
                    <Select placeholder="Select role">
                      <Option value="developer">Developer</Option>
                      <Option value="designer">Designer</Option>
                      <Option value="manager">Manager</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="birthDate" label="Birth Date">
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="gender" label="Gender">
                    <Radio.Group>
                      <Radio value="male">Male</Radio>
                      <Radio value="female">Female</Radio>
                      <Radio value="other">Other</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item name="experience" label="Years of Experience">
                <Slider min={0} max={20} marks={{ 0: '0', 10: '10', 20: '20' }} />
              </Form.Item>

              <Form.Item name="rating" label="Skill Rating">
                <Rate />
              </Form.Item>

              <Form.Item name="notifications" valuePropName="checked">
                <Checkbox>Receive notifications</Checkbox>
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading} block>
                  Submit
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* Components Showcase */}
        <Col xs={24} lg={12}>
          <Space direction="vertical" size="large" className="w-full">
            {/* Alerts */}
            <Card title="Alerts & Messages">
              <Space direction="vertical" className="w-full">
                <Alert message="Success" type="success" showIcon />
                <Alert message="Info" type="info" showIcon />
                <Alert message="Warning" type="warning" showIcon />
                <Alert message="Error" type="error" showIcon />
              </Space>
            </Card>

            {/* Progress & Stats */}
            <Card title="Progress & Statistics">
              <Space direction="vertical" className="w-full">
                <div>
                  <Text>Loading Progress</Text>
                  <Progress percent={75} status="active" />
                </div>
                <div>
                  <Text>Success Rate</Text>
                  <Progress percent={90} status="success" />
                </div>
              </Space>
            </Card>

            {/* Interactive Elements */}
            <Card title="Interactive Elements">
              <Space wrap>
                <Switch defaultChecked />
                <Button type="primary" onClick={() => message.info('Button clicked!')}>
                  Click Me
                </Button>
                <Upload>
                  <Button icon={<UploadOutlined />}>Upload</Button>
                </Upload>
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>

      {/* Table Section */}
      <Card title="Data Table" className="mt-8">
        <Table columns={tableColumns} dataSource={tableData} pagination={false} />
      </Card>

      {/* Modal */}
      <Modal
        title="Form Submitted Successfully!"
        open={visible}
        onOk={() => setVisible(false)}
        onCancel={() => setVisible(false)}
      >
        <p>Your registration form has been submitted successfully!</p>
        <p>Check the console for form data.</p>
      </Modal>
    </div>
  );
}
