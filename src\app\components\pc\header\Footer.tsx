"use client";

import Link from "next/link";
import Image from "next/image";
import React from "react";

const footerLinks = [
  { label: "About Us", href: "/about" },
  { label: "Privacy", href: "/privacy" },
  { label: "Terms", href: "/terms" },
  { label: "Security and Compliance", href: "/security" },
  { label: "Contact", href: "/contact" },
  { label: "Status", href: "/status" },
];

const FooterPC: React.FC = () => {
  return (
    <footer className="bg-gray-800 text-gray-300 text-sm relative bottom-0">
      <div className="w-content mx-auto px-6 py-6 flex items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center gap-2">
          <span className="relative h-6 w-6">
            <Image src="/assets/svg/fast-logo.svg" alt="FASTconvert" fill />
          </span>
          <span className="font-bold text-[35px] text-white">
            <span className="text-primary">FAST</span>convert
          </span>
        </Link>

        {/* Menu */}
        <nav className="hidden md:flex gap-6 text-gray-300">
          {footerLinks.map((item) => (
            <Link
              key={item.label}
              href={item.href}
              className="hover:text-white transition text-18 font-light"
            >
              {item.label}
            </Link>
          ))}
        </nav>

        {/* Language selector */}
        <div className="relative group cursor-pointer text-18">
          <span className="flex items-center gap-1 hover:text-white">
            English
            <svg
              className="h-3 w-3"
              viewBox="0 0 16 16"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.6"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M4 6l4 4 4-4" />
            </svg>
          </span>
          <div
            className="invisible group-hover:visible opacity-0 group-hover:opacity-100
                       transition-all duration-150 absolute right-0 mt-2
                       rounded-md border border-gray-700 bg-gray-900 shadow-lg"
          >
            <button className="block px-4 py-2 w-full text-left hover:bg-gray-700">
              English
            </button>
            <button className="block px-4 py-2 w-full text-left hover:bg-gray-700">
              Vietnamese
            </button>
          </div>
        </div>
      </div>

      {/* Bottom bar */}
      <div className="bg-gray-900 text-center py-3 text-base text-gray-400">
        Fastconvertforvideo © {new Date().getFullYear()}
      </div>
    </footer>
  );
};

export default FooterPC;
