"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON>, Typography, Row, Col, Input, Select } from 'antd';
import { SearchOutlined, UserOutlined, SettingOutlined } from '@ant-design/icons';
import Link from 'next/link';

const { Title, Paragraph } = Typography;
const { Search } = Input;

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <Title level={1} className="text-4xl font-bold text-gray-800 mb-4">
            Welcome to html_next_apps
          </Title>
          <Paragraph className="text-lg text-gray-600">
            A Next.js project with Ant Design components
          </Paragraph>
        </div>

        {/* Search Section */}
        <Card className="mb-8 shadow-sm">
          <Row gutter={16} align="middle">
            <Col flex="auto">
              <Search
                placeholder="Search for anything..."
                size="large"
                enterButton={<SearchOutlined />}
              />
            </Col>
            <Col>
              <Select
                defaultValue="all"
                size="large"
                style={{ width: 120 }}
                options={[
                  { value: 'all', label: 'All' },
                  { value: 'projects', label: 'Projects' },
                  { value: 'users', label: 'Users' },
                ]}
              />
            </Col>
          </Row>
        </Card>

        {/* Feature Cards */}
        <Row gutter={[24, 24]} className="mb-8">
          <Col xs={24} sm={12} lg={8}>
            <Card 
              hoverable 
              className="h-full shadow-sm"
              actions={[
                <Button type="link" icon={<UserOutlined />}>Learn More</Button>
              ]}
            >
              <Title level={3} className="mb-3">Next.js 15</Title>
              <Paragraph className="text-gray-600">
                Built with the latest Next.js featuring App Router, Server Components, and Turbopack for blazing fast development.
              </Paragraph>
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={8}>
            <Card 
              hoverable 
              className="h-full shadow-sm"
              actions={[
                <Button type="link" icon={<SettingOutlined />}>Configure</Button>
              ]}
            >
              <Title level={3} className="mb-3">Ant Design</Title>
              <Paragraph className="text-gray-600">
                Beautiful and powerful React UI library with enterprise-level design language and rich components.
              </Paragraph>
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={8}>
            <Card 
              hoverable 
              className="h-full shadow-sm"
              actions={[
                <Button type="link">Explore</Button>
              ]}
            >
              <Title level={3} className="mb-3">TypeScript</Title>
              <Paragraph className="text-gray-600">
                Full TypeScript support for better development experience with type safety and IntelliSense.
              </Paragraph>
            </Card>
          </Col>
        </Row>

        {/* Action Buttons */}
        <div className="text-center">
          <Space size="large">
            <Link href="/antd-showcase">
              <Button type="primary" size="large">
                View Ant Design Showcase
              </Button>
            </Link>
            <Button size="large">
              View Documentation
            </Button>
            <Button size="large">
              GitHub Repository
            </Button>
          </Space>
        </div>
      </div>
    </div>
  );
}
