"use client";

import Image from "next/image";

type SuccessModalProps = {
  isOpen: boolean;
  title: string;
  onContinue: () => void;
};

export default function SuccessModal({ isOpen, title, onContinue }: SuccessModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-60 flex items-center justify-center ">
      <div className="bg-primary rounded-sm p-6 max-w-sm w-full text-center">
        <div className="mb-6">
          
          
          {/* Success Message */}
          <h3 className="text-[#E5E2E5] text-lg font-regular leading-relaxed max-[376px]:text-sm mb-10 ">
            {title}
          </h3>
        </div>
        
        {/* Continue Button */}
        <button
          onClick={onContinue}
          className="w-full bg-[#6E4ADA] text-white  rounded-full py-3 text-center text-2xl font-medium max-[376px]:text-base "
        >
          TIẾP TỤC
        </button>
      </div>
    </div>
  );
}
