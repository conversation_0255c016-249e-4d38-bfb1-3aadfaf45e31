// lib/device.ts
import { headers } from "next/headers";

export type DeviceInfo = {
  userAgent: string;
  isMobile: boolean;
  isDesktop: boolean;
};

export async function getDeviceInfo(): Promise<DeviceInfo> {
  const headersList = await headers();
  const ua = headersList.get("user-agent") || "";

  const isMobile =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua);

  return {
    userAgent: ua,
    isMobile,
    isDesktop: !isMobile,
  };
}
