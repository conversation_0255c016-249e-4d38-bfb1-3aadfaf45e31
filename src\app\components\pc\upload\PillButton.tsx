import React from "react";

type PillButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement>;

const PillButton = ({ className, onClick, ...props }: PillButtonProps) => {
  const base =
    "group inline-flex items-center h-12 rounded-full " +
    "bg-[#5B34D6] hover:bg-[#4E2EC5] active:bg-[#4528B2] " +
    "text-white font-semibold text-base md:text-lg " +
    "pl-5 pr-2 shadow-lg ring-1 ring-black/5 transition " +
    "disabled:opacity-60 disabled:cursor-not-allowed";
  return (
    <button
      onClick={onClick}
      className={`${base} ${className ?? ""}`}
      {...props}
    >
      <span className="flex items-center gap-2 pr-4">
        {/* Left icon here */}
        <span>
          <svg
            width="22"
            height="26"
            viewBox="0 0 22 26"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M21.0994 7.16324L14.7081 0.422501C14.5808 0.288406 14.4297 0.182075 14.2635 0.109581C14.0972 0.0370872 13.919 -0.000148757 13.7391 4.46621e-07H2.78261C2.17722 4.46621e-07 1.59663 0.253637 1.16856 0.705114C0.740488 1.15659 0.5 1.76892 0.5 2.40741V23.5926C0.5 24.2311 0.740488 24.8434 1.16856 25.2949C1.59663 25.7464 2.17722 26 2.78261 26H19.2174C19.8228 26 20.4034 25.7464 20.8314 25.2949C21.2595 24.8434 21.5 24.2311 21.5 23.5926V8.18519C21.5001 7.99544 21.4648 7.80753 21.3961 7.63218C21.3274 7.45683 21.2265 7.29749 21.0994 7.16324ZM14.6522 4.4537L17.2772 7.22222H14.6522V4.4537ZM3.23913 23.1111V2.88889H11.913V8.66667C11.913 9.04976 12.0573 9.41716 12.3142 9.68804C12.571 9.95893 12.9194 10.1111 13.2826 10.1111H18.7609V23.1111H3.23913Z"
              fill="white"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M11 22C14.0376 22 16.5 19.5376 16.5 16.5C16.5 13.4624 14.0376 11 11 11C7.96243 11 5.5 13.4624 5.5 16.5C5.5 19.5376 7.96243 22 11 22ZM10.3679 20.4482V17.8182H7.73793V16.0384H10.3679V13.4084H12.1477V16.0384H14.7777V17.8182H12.1477V20.4482H10.3679Z"
              fill="white"
            />
          </svg>
        </span>
        {/* <YourIcon className="h-5 w-5" /> */}
        <span>Choose Files</span>
      </span>

      {/* Right segment with divider + chevron */}
      <span className="relative -my-2 ml-1 flex h-12 w-10 items-center justify-center">
        <span aria-hidden className="absolute left-0 h-full w-px bg-white" />
        <svg
          className="h-4 w-4 transition-transform group-hover:translate-y-0.5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path d="M5.23 7.21a.75.75 0 0 1 1.06.02L10 11.086l3.71-3.856a.75.75 0 0 1 1.08 1.04l-4.24 4.41a.75.75 0 0 1-1.08 0l-4.24-4.41a.75.75 0 0 1 .02-1.06z" />
        </svg>
      </span>
    </button>
  );
};

export default PillButton;
