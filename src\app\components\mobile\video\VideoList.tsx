"use client";

import Image from "next/image";
import VideoItem from "./VideoItem";

const videoData = [
  {
    id: 1,
    title: "Survival in the deep forest",
    thumbnail: "/assets/images/Video1.png",
    size: "32MB",
    duration: "00:00:25",
    date: "Today"
  },
  {
    id: 2,
    title: "Đại tướng <PERSON>",
    thumbnail: "/assets/images/Video2.png",
    size: "32MB",
    duration: "00:00:25",
  },
  {
    id: 3,
    title: "Nữ quái công an",
    thumbnail: "/assets/images/Video3.png",
    size: "32MB",
    duration: "00:00:25",
    date: "Tháng 8"
  },
  {
    id: 4,
    title: "Nữ Đ<PERSON>",
    thumbnail: "/assets/images/Video4.png",
    size: "32MB",
    duration: "00:00:25",
  },
  {
    id: 5,
    title: "<PERSON><PERSON>ón<PERSON> tiếp viên hàng không",
    thumbnail: "/assets/images/Video5.png",
    size: "32MB",
    duration: "00:00:25",
    date: "Tháng 7"
  },
];

type VideoListProps = {
  viewMode?: "grid" | "list";
  selectedVideos?: Set<number>;
  onVideoSelect?: (videoId: number) => void;
};

export default function VideoList({ viewMode = "list", selectedVideos = new Set(), onVideoSelect }: VideoListProps) {
  if (viewMode === "grid") {
    return (
      <div className="p-4">
        <div className="grid grid-cols-2 gap-3">
          {videoData.map((video) => (
            <div 
              key={video.id}
              onClick={() => onVideoSelect?.(video.id)}
              className={`relative cursor-pointer ${selectedVideos.has(video.id) ? 'ring-2 ring-blue-400' : ''}`}
            >
              <VideoItem
                title={video.title}
                thumbnail={video.thumbnail}
                size={video.size}
                duration={video.duration}
                date={video.date}
              /> 
            </div>
          ))}
        </div>
      </div>
    );
  }

  // List view (default)
  return (
    <div className="p-3 space-y-4">
      {/* Date Groups */}
      <div>
        <h3 className="font-light text-lg mb-2  text-gray-1">Today</h3>
        <div className="my-1 font-light text-lg tracking-[0]">
          {videoData.filter(v => v.date === "Today").map((video) => (
            <div 
              key={video.id} 
              onClick={() => onVideoSelect?.(video.id)}
              className={`flex gap-1 cursor-pointer  ${selectedVideos.has(video.id) ? 'bg-blue-500/20 ring-2 ring-blue-400' : 'hover:bg-gray-800/50'}`}
            >
              <div className="relative w-36 h-24  overflow-hidden flex-shrink-0">
                <Image
                  src={video.thumbnail}
                  alt={video.title}
                  fill
                  className="object-cover w-36 h-24"
                />
               
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-white font-normal text-lg tracking-[0] mb-[52px]">
                  {video.title}
                </h4>
                <div className="flex items-center gap-6 font-normal text-sm  tracking-[0]">
                  <span>{video.size}</span>
                  <span>{video.duration}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="font-light text-lg tracking-[0] mb-[10px]">Tháng 8</h3>
        <div className="space-y-3">
          {videoData.filter(v => v.date === "Tháng 8").map((video) => (
            <div 
              key={video.id} 
              onClick={() => onVideoSelect?.(video.id)}
              className={`flex gap-3 cursor-pointer  ${selectedVideos.has(video.id) ? 'bg-blue-500/20 ring-2 ring-blue-400' : 'hover:bg-gray-800/50'}`}
            >
              <div className="relative w-36 h-24  overflow-hidden flex-shrink-0">
                <Image
                  src={video.thumbnail}
                  alt={video.title}
                  fill
                  className="object-cover w-36 h-24"
                />
                
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-white font-normal text-lg tracking-[0] mb-[52px]">
                  {video.title}
                </h4>
                <div className="flex items-center gap-6 font-normal text-sm  tracking-[0]">
                  <span>{video.size}</span>
                  <span>{video.duration}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="font-light text-lg tracking-[0] mb-[10px]">Tháng 7</h3>
        <div className="space-y-3">
          {videoData.filter(v => v.date === "Tháng 7").map((video) => (
            <div 
              key={video.id} 
              onClick={() => onVideoSelect?.(video.id)}
              className={`flex gap-3 cursor-pointer  ${selectedVideos.has(video.id) ? 'bg-blue-500/20 ring-2 ring-blue-400' : 'hover:bg-gray-800/50'}`}
            >
              <div className="relative w-36 h-24  overflow-hidden flex-shrink-0">
                <Image
                  src={video.thumbnail}
                  alt={video.title}
                  fill
                  className="object-cover w-36 h-24"
                />
               
              </div>
              <div className="flex-1 min-w-0 flex flex-col justify-between">
                <h4 className="text-white font-normal text-lg tracking-[0] ">
                  {video.title}
                </h4>
                <div className="flex items-center gap-6 font-normal text-sm  tracking-[0]">
                  <span>{video.size}</span>
                  <span>{video.duration}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
