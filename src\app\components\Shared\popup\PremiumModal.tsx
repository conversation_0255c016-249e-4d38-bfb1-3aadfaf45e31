"use client";

import { useState } from "react";
import Image from "next/image";

type PremiumModalProps = {
  open: boolean;
  onClose: () => void;
};

const features = [
  {
    name: "Batch compression",
    free: "3 video",
    premium: "Unlimited",
    available: false,
  },
  {
    name: "No ads",
    free: false,
    premium: true,
    available: false,
  },
  {
    name: "Keep original metadata",
    free: false,
    premium: true,
    available: false,
  },
  {
    name: "Video editing",
    free: false,
    premium: true,
    available: false,
  },
  {
    name: "No promo text",
    free: false,
    premium: true,
    available: false,
  },
  {
    name: "Email support",
    free: false,
    premium: true,
    available: false,
  },
  {
    name: "Support future development",
    free: false,
    premium: true,
    available: false,
  },
];

export default function PremiumModal({ open, onClose }: PremiumModalProps) {
  const [selectedPlan, setSelectedPlan] = useState<
    "3months" | "1year" | "lifetime"
  >("1year");

  if (!open) return null;

  return (
    <>
      {/* Modal */}
      <div className="fixed inset-0 z-50 flex items-center justify-center h-screen scrollbar-hide overflow-y-auto scrollbar-width-none ">
        <div className="w-full h-full  bg-foreground rounded-lg text-white relative pt-3 max-[376px]:pt-2  py-5 ">
          {/* Close Button */}
          <button onClick={onClose} className="absolute top-5 left-5 text-2xl text-white max-[376px]:text-xl  ">
            <Image src="/assets/svg/close.svg" alt="X" width={20} height={20} className="w15 h15 max-[376px]:w-3 max-[376px]:h-3" />
          </button>

          <div className="p-4">
            {/* App Icon */}
            <div className="flex justify-center mb-1 max-[376px]:mb-0.5 mt-13 max-[376px]:mt-2">
              <div className="relative w-24 h-24 max-[376px]:w-16 max-[376px]:h-16">
                <Image
                  src="/assets/svg/fast-logo.svg"
                  alt="Fast Converter Logo"
                  objectFit="cover"
                  width={100}
                  height={100}
                  className="w28 h28  max-[376px]:w20 max-[376px]:h20"
                />
              </div>
            </div>

            {/* Title */}
            <h2 className="font-medium text-3xl text-center text-primary mb-3 max-[376px]:text-xl max-[376px]:mb-2 ">
              Upgrade to Premium
            </h2>

            {/* Features Table */}
            <div className="mb-6 ">
              <div className=" bg-foreground-2 grid grid-cols-12 gap-2 mb-4  font-medium text-sm max-[376px]:text-sm py-3 px-2 rounded-lg max-[376px]:py-1 max-[376px]:mb-1">
                <div className="col-span-7 text-lg max-[376px]:py-1 max-[376px]:px-0.5 max-[376px]:text-base ">Features</div>
                <div className="col-span-2 text-center text-base max-[376px]:text-sm  ">Free</div>
                <div className="col-span-3 text-center text-base max-[376px]:text-sm ">Premium</div>
              </div>

              {features.map((feature, index) => (
                <div
                  key={index}
                  className="grid grid-cols-12 gap-2 py-2 border-b border-gray-700/20 last:border-b-0 max-[376px]:py-1 max-[376px]:gap-1 max-[376px]:text-base "
                >
                  <div className="col-span-7 font-regular tracking-[0] max-[376px]:text-base">
                    {feature.name}
                  </div>
                  <div className="col-span-2 text-center font-regular text-lg tracking-[0] flex justify-center max-[376px]:text-base">
                    {typeof feature.free === "string" ? (
                      <span className="text-base text-white">{feature.free}</span>
                    ) : feature.free ? (
                      <Image src="/assets/svg/check.svg" alt="Check" width={20} height={20} className="w-4 h-4 max-[376px]:w-3 max-[376px]:h-3" />
                    ) : (
                    <Image src="/assets/svg/X.svg" alt="X" width={20} height={20} className="w-4 h-4 max-[376px]:w-3 max-[376px]:h-3" />
                    )}
                  </div>
                  <div className="col-span-3 text-center font-regular text-lg tracking-[0] flex justify-center max-[376px]:text-base">
                    {typeof feature.premium === "string" ? (
                      <span className="text-base text-primary max-[376px]:text-sm">
                        {feature.premium}
                      </span>
                    ) : feature.premium ? (
                      <Image src="/assets/svg/check.svg" alt="Check" width={20} height={20} className="w-4 h-4 max-[376px]:w-3 max-[376px]:h-3" />
                    ) : (
                      <Image src="/assets/svg/X.svg" alt="X" width={20} height={20} className="w-4 h-4 max-[376px]:w-3 max-[376px]:h-3" />
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Subscriptions */}
            <div className="mb-4">
              <h3 className=" text-[#969494] mb-2 max-[376px]:text-base max-[376px]:mb-1">
              Subscriptions
            </h3>

              {/* 3 Months */}
              <button
                onClick={() => setSelectedPlan("3months")}
                className={`w-full rounded-full flex justify-between 
             border mb-3 py-2 px-6 max-[376px]:py-1 max-[376px]:px-6 border-1 border-solid ${
              selectedPlan === "3months"
                ? "bg-primary border-transparent"
                : "border-primary"
            } flex items-center justify-between`}
              >
                <div className="flex items-center gap-3 max-[376px]:gap-2">
                  <div className="w-6 h-6  rounded flex items-center justify-center max-[376px]:w4 max-[376px]:h4 ">
                      <Image src="/assets/svg/Vector.svg" alt="Check" width={22} height={22} className="w15 h15 max-[376px]:w10 max-[376px]:h10" />
                    </div>
                  <span className="text-lg leading-10 max-[376px]:text-base ">3 MONTHS</span>
                </div>
                                  <div className="text-right">
                    <div className="text-2xl leading-10 font-semibold max-[376px]:text-xl max-[376px]:leading-8 max-[376px]:font-normal">$1.99</div>
                   
                  </div>
                </button>

            </div>

            <div className={`flex justify-between mt-2 py-3 px-6 border-1 border-solid rounded-full max-[376px]:py-2 max-[376px]:px-6 ${selectedPlan === "1year" ? "bg-primary border-transparent" : "border-primary"}`}>
              <button className="flex items-center gap-2 justify-between w-full" onClick={() => setSelectedPlan("1year")} >
                <div className="flex items-center gap-2">
                  <Image src="/assets/svg/Vector.svg" alt="Check" width={22} height={22} className="w15 h15 max-[376px]:w10 max-[376px]:h10" />
                  <p className="text-lg font-regular leading-6">1 YEAR</p>
                </div>
                  <p className="text-2xl font-bold max-[376px]:text-xl ">$5.99</p>
              </button>
            </div>

            <p className="text-base font-regular text-[#969494] mt-6 mb-1 max-[376px]:mt-2 max-[376px]:mb-0.5">
            One-time purchase
            </p>
            <div className={`flex justify-between px-6 py-3 border-1 border-solid rounded-full max-[376px]:py-2 ${selectedPlan === "lifetime" ? "bg-primary border-transparent" : "border-primary"}`}>
             <button className="flex items-center gap-2 justify-between w-full" onClick={() => setSelectedPlan("lifetime")}>
              <div className="flex items-center gap-2">
                <Image src="/assets/svg/Vector.svg" alt="Check" width={22} height={22} className="w15 h15 max-[376px]:w10 max-[376px]:h10" />
                <p className="text-lg font-regular leading-6">LIFETIME</p>
              </div>
              <p className="text-2xl font-bold max-[376px]:text-xl ">$9.99</p>
             </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
