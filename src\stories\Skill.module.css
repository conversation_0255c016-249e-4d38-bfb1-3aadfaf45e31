/* CSS Module chỉ cho skill effects và animations */

/* Animated skill card */
.animatedSkill {
  animation: skillFloat 4s ease-in-out infinite;
}

@keyframes skillFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

/* Skill icon container */
.iconContainer {
  position: relative;
  overflow: hidden;
}

.skillIcon {
  transition: all 0.3s ease;
}

.animatedSkill:hover .skillIcon {
  transform: scale(1.1) rotate(5deg);
}

/* Skill name animation */
.skillName {
  animation: nameGlow 3s ease-in-out infinite alternate;
}

@keyframes nameGlow {
  0% {
    text-shadow: none;
  }
  100% {
    text-shadow: 0 0 4px currentColor;
  }
}

/* Locked skill effects */
.lockedSkill {
  position: relative;
}

.lockOverlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: inherit;
}

/* Max level effects */
.maxLevelSkill {
  animation: maxLevelGlow 2s ease-in-out infinite alternate;
}

@keyframes maxLevelGlow {
  0% {
    box-shadow: 0 0 10px rgba(251, 191, 36, 0.3);
  }
  100% {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.6);
  }
}

.maxLevelCrown {
  position: absolute;
  top: -2px;
  right: -2px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: crownBounce 2s ease-in-out infinite;
}

@keyframes crownBounce {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-2px) rotate(10deg);
  }
}

/* Progress bar */
.progressContainer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 0 0 8px 8px;
}

.progressBar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: inherit;
  transition: width 0.5s ease;
  animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

/* Rarity glow effects */
.rarityGlow {
  position: absolute;
  inset: -4px;
  border-radius: 12px;
  opacity: 0.6;
  animation: rarityPulse 3s ease-in-out infinite alternate;
  pointer-events: none;
}

@keyframes rarityPulse {
  0% {
    opacity: 0.4;
  }
  100% {
    opacity: 0.8;
  }
}

.glow-rare {
  background: radial-gradient(ellipse, rgba(59, 130, 246, 0.3), transparent);
}

.glow-epic {
  background: radial-gradient(ellipse, rgba(139, 92, 246, 0.4), transparent);
}

.glow-legendary {
  background: radial-gradient(ellipse, rgba(251, 191, 36, 0.5), transparent);
}

/* Rarity specific animations */
.rarity-rare.animatedSkill {
  animation: rareShimmer 4s ease-in-out infinite;
}

@keyframes rareShimmer {
  0%,
  100% {
    transform: translateY(0px);
    filter: brightness(1);
  }
  50% {
    transform: translateY(-2px);
    filter: brightness(1.1);
  }
}

.rarity-epic.animatedSkill {
  animation: epicPulse 3s ease-in-out infinite;
}

@keyframes epicPulse {
  0%,
  100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-2px) scale(1.02);
  }
}

.rarity-legendary.animatedSkill {
  animation: legendaryFloat 2s ease-in-out infinite;
}

@keyframes legendaryFloat {
  0%,
  100% {
    transform: translateY(0px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
}

/* Unlock effect */
.unlockEffect {
  position: absolute;
  inset: 0;
  background: radial-gradient(
    circle,
    rgba(34, 197, 94, 0.3) 0%,
    transparent 70%
  );
  animation: unlockPulse 0.8s ease-out;
  pointer-events: none;
  opacity: 0;
}

@keyframes unlockPulse {
  0% {
    opacity: 1;
    transform: scale(0.5);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}

/* Hover enhancements */
.animatedSkill:hover {
  animation-duration: 1s;
}

.animatedSkill:hover .rarityGlow {
  animation-duration: 1s;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .animatedSkill {
    animation: none;
  }

  .maxLevelCrown {
    width: 16px;
    height: 16px;
    font-size: 0.7em;
  }

  .progressContainer {
    height: 2px;
  }
}
