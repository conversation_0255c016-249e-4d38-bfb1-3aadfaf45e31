# 📘 Tài liệu mô tả sản phẩm: Ứng dụng học tiếng Anh qua **Truyện Chêm**

## 1. Giới thiệu

### 1.1 Truyện Chêm là gì?  
"Truyện Chêm" là một phương pháp học ngoại ngữ độc đáo, trong đó câu chuyện được kể bằng ngôn ngữ mẹ đẻ (tiếng Việt) và được **“chêm”** thêm các từ hoặc cụm từ tiếng Anh.  
- Ví dụ: "Hôm qua mình đi **shopping** và mua một cái **dress** rất đẹp."  
- Ưu điểm:
  - Học từ vựng trong **ngữ cảnh tự nhiên**.  
  - Ghi nhớ lâu hơn nhờ kết hợp ngôn ngữ quen thuộc (tiếng Việt) và từ mới (tiếng Anh).  
  - Gi<PERSON>m áp lực học ngoại ngữ, tạo cảm gi<PERSON><PERSON> gầ<PERSON> gũ<PERSON>, vui vẻ.  
- Phương pháp này được người Do Thái áp dụng thành công trong việc học nhiều ngoại ngữ.

### 1.2 Mục tiêu sản phẩm  
Ứng dụng hỗ trợ học viên học tiếng Anh thông qua **Truyện Chêm** kết hợp công nghệ AI, bao gồm:  
- Sinh câu chuyện chêm tự động theo chủ đề.  
- Đọc và phát âm chuẩn giọng bản xứ.  
- Giúp người dùng tra cứu, quản lý và chia sẻ câu chuyện.  
- Rèn luyện nghe, nói thông qua hội thoại giả lập bằng giọng nói.  

---

## 2. Phân tích chi tiết tính năng

### 2.1 Sinh câu chuyện chêm bằng AI
- AI gợi ý hoặc tự động sinh câu chuyện dựa theo:  
  - Chủ đề (family, travel, shopping, work, school, …).  
  - Cấp độ từ vựng (A1 → C1).  
  - Độ dài mong muốn (ngắn, trung bình, dài).  
- Người dùng có thể mô tả ý tưởng và AI sẽ viết truyện chêm theo yêu cầu.  

### 2.2 Đọc tự động câu chuyện
- Tích hợp công nghệ **Text-to-Speech (TTS)**: đọc toàn bộ câu chuyện bằng giọng tự nhiên (Anh-Anh, Anh-Mỹ).  
- Tùy chọn:  
  - Đọc chậm/tốc độ chuẩn.  
  - Highlight từ tiếng Anh khi đọc.  

### 2.3 Quản lý từ vựng trong truyện
- Danh sách từ vựng được tự động trích xuất từ truyện.  
- Tính năng tra cứu chi tiết:  
  - Nghĩa tiếng Việt.  
  - Phiên âm IPA.  
  - Ví dụ minh họa.  
  - Gợi ý cách sử dụng trong câu khác.  
- Có thể phát âm mẫu cho từng từ.  

### 2.4 Quản lý câu chuyện
- Người dùng có thể:  
  - ✅ Thêm vào danh sách yêu thích.  
  - ✅ Công khai câu chuyện vào **Kho Truyện Chung**.  
  - ✅ Xóa hoặc ẩn câu chuyện không cần thiết.  
  - ✅ Tạo nhiều **Tủ truyện riêng** để phân loại (VD: "Truyện du lịch", "Truyện học tập").  
  - ✅ Thêm câu chuyện từ người dùng khác vào **Tủ sách cá nhân**.  

### 2.5 Luyện phát âm từ vựng
- Ứng dụng sử dụng **AI Speech Recognition** để:  
  - So sánh phát âm của người dùng với chuẩn bản ngữ.  
  - Phát hiện lỗi sai (ví dụ: thiếu âm cuối, nhấn sai trọng âm).  
  - Đưa ra gợi ý sửa kèm phát âm mẫu.  
- Hỗ trợ đọc từng từ/cụm từ hoặc cả câu.  

### 2.6 Giao tiếp bằng giọng nói (Speaking Practice)
- Tính năng luyện hội thoại tương tác:  
  - AI tạo **kịch bản hội thoại** theo chủ đề hằng ngày (đi mua sắm, gọi món ăn, hỏi đường, phỏng vấn xin việc…).  
  - Người dùng đóng 1 vai, AI đóng vai còn lại.  
  - AI lắng nghe và phản hồi theo kịch bản.  
- Nếu người dùng nói sai:  
  - AI phát hiện lỗi sai.  
  - Giải thích tại sao sai.  
  - Đưa ra gợi ý sửa và cho người dùng thực hành lại.  

---

## 3. User Stories

### 3.1 Truyện Chêm
- **Là học viên**, tôi muốn nhập một chủ đề để AI sinh ra một câu chuyện chêm, để tôi có thể học từ vựng theo ngữ cảnh.  
- **Là học viên**, tôi muốn mô tả ý tưởng (ví dụ: "câu chuyện về chuyến đi Đà Nẵng"), để AI tạo ra câu chuyện phù hợp.  

### 3.2 Đọc và nghe
- **Là học viên**, tôi muốn nghe ứng dụng đọc câu chuyện bằng giọng chuẩn, để luyện kỹ năng nghe.  
- **Là học viên**, tôi muốn điều chỉnh tốc độ đọc, để dễ theo dõi và học từ mới.  

### 3.3 Quản lý từ vựng
- **Là học viên**, tôi muốn xem danh sách từ vựng trong câu chuyện, để dễ ôn tập.  
- **Là học viên**, tôi muốn nhấn vào từ vựng để tra nghĩa và nghe phát âm, để hiểu sâu hơn.  

### 3.4 Quản lý câu chuyện
- **Là học viên**, tôi muốn thêm câu chuyện vào danh sách yêu thích, để lưu lại cho lần học sau.  
- **Là học viên**, tôi muốn công khai câu chuyện mình tạo, để chia sẻ với cộng đồng.  
- **Là học viên**, tôi muốn thêm câu chuyện của người khác vào tủ sách cá nhân, để có thêm tài liệu học.  

### 3.5 Luyện phát âm
- **Là học viên**, tôi muốn đọc lại từ/câu trong truyện và được ứng dụng đánh giá, để cải thiện phát âm.  
- **Là học viên**, tôi muốn biết lỗi phát âm của mình (như thiếu âm cuối, phát âm sai trọng âm), để sửa ngay lập tức.  

### 3.6 Giao tiếp bằng giọng nói
- **Là học viên**, tôi muốn luyện nói với AI trong tình huống thực tế, để cải thiện kỹ năng giao tiếp.  
- **Là học viên**, tôi muốn AI giải thích khi tôi nói sai, để học cách sửa lỗi.  

---

## 4. Kiến trúc giải pháp (overview)
- **AI NLP Engine**: tạo truyện chêm + phân tích ngôn ngữ.  
- **TTS Engine**: đọc truyện bằng giọng tự nhiên.  
- **Speech Recognition & Scoring**: đánh giá phát âm, phát hiện lỗi sai.  
- **Vocabulary Manager**: trích xuất và quản lý từ vựng.  
- **Story Repository**: quản lý câu chuyện, tủ sách cá nhân và kho truyện chung.  

---

## 5. Kết luận
Ứng dụng học tiếng Anh qua **Truyện Chêm + AI** không chỉ giúp học viên **học từ vựng theo ngữ cảnh tự nhiên**, mà còn hỗ trợ **nghe - nói - phát âm chuẩn** thông qua các tính năng tương tác AI.  
Đây sẽ là một công cụ **toàn diện** giúp người học tiếp cận tiếng Anh một cách dễ dàng, thú vị và hiệu quả.

