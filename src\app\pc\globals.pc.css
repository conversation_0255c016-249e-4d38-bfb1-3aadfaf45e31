@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #4e2eaf;
  --primary2: #7858db;
  --purple-1: #4020a1;
  --purple-btn: #6e4ada;
  --dark-gray-1: #1c1c1c;
  --dark-gray-2: #2a2929;
  --font-kanit: "Kanit", sans-serif;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-kanit);
  width: 100vw;
  overflow-x: hidden;
}

main {
  width: 100vw;
}

@theme {
  --text-18: 18px;
  --text-22: 22px;
  --color-purple-btn: var(--purple-btn);
  --color-dark-gray-1: var(--dark-gray-1);
  --color-dark-gray-2: var(--dark-gray-2);
  --color-primary: var(--primary);
  --color-primary-2: var(--primary2);
  --color-purple-1: var(--purple-1);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

@keyframes popup-enter {
  from {
    opacity: 0;
    transform: translateY(8px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
