"use client";
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

type Source = { src: string; type?: string };

type MyVideoProps = {
  /** Single URL string or an array of <source> descriptors */
  src: string | Source[];
  /** Video poster image */
  poster?: string;
  /** Show native controls */
  controls?: boolean;
  /** Autoplay when user clicks overlay */
  autoPlayOnClick?: boolean;
  /** Start muted (recommended for mobile inline playback) */
  muted?: boolean;
  /** Loop playback */
  loop?: boolean;
  /** Aspect ratio utility (e.g. 'aspect-video', 'aspect-[9/16]') */
  aspectClassName?: string;
  /** Extra classes for the outer container */
  className?: string;
  /** Tailwind rounding class, e.g. 'rounded-xl' */
  roundedClassName?: string;
  /** Optional caption below */
  caption?: string;
  /** Show big play overlay button */
  showPlayOverlay?: boolean;
  /** Callback events */
  onPlay?: () => void;
  onPause?: () => void;
};

/**
 * MyVideo
 * - Mobile-friendly, accessible video with centered play overlay
 * - Works well in Next.js / React apps (no browser APIs on SSR path)
 */
const MyVideo = forwardRef<HTMLVideoElement, MyVideoProps>(
  (
    {
      src,
      poster,
      controls = false,
      autoPlayOnClick = true,
      muted = false,
      loop = false,
      aspectClassName = "",
      className = "",
      roundedClassName = "",
      caption,
      showPlayOverlay = true,
      onPlay,
      onPause,
    },
    ref
  ) => {
    const videoRef = useRef<HTMLVideoElement | null>(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [hasInteracted, setHasInteracted] = useState(false);

    useImperativeHandle(ref, () => videoRef.current as HTMLVideoElement);

    // Handlers
    const play = useCallback(async () => {
      if (!videoRef.current) return;
      try {
        setHasInteracted(true);
        await videoRef.current.play();
      } catch (e) {
        // Autoplay might be blocked
        console.warn("Play blocked or failed", e);
      }
    }, []);

    const pause = useCallback(() => {
      if (!videoRef.current) return;
      videoRef.current.pause();
    }, []);

    const toggle = useCallback(() => {
      if (isPlaying) pause();
      else play();
    }, [isPlaying, pause, play]);

    // Events
    useEffect(() => {
      const v = videoRef.current;
      if (!v) return;
      const onCanPlay = () => setIsLoading(false);
      const onPlayEvt = () => {
        setIsPlaying(true);
        onPlay?.();
      };
      const onPauseEvt = () => {
        setIsPlaying(false);
        onPause?.();
      };
      v.addEventListener("canplay", onCanPlay);
      v.addEventListener("play", onPlayEvt);
      v.addEventListener("pause", onPauseEvt);
      return () => {
        v.removeEventListener("canplay", onCanPlay);
        v.removeEventListener("play", onPlayEvt);
        v.removeEventListener("pause", onPauseEvt);
      };
    }, [onPause, onPlay]);

    // Render helpers
    const renderSources = () => {
      if (typeof src === "string") {
        return <source src={src} />;
      }
      return src.map((s, i) => <source key={i} src={s.src} type={s.type} />);
    };

    return (
      <figure className={`w-full h-full ${className}`}>
        <div
          className={`relative ${aspectClassName} bg-black overflow-hidden ${roundedClassName}`}
          onClick={() => autoPlayOnClick && toggle()}
          role={autoPlayOnClick ? "button" : undefined}
          aria-label={isPlaying ? "Pause video" : "Play video"}
          tabIndex={autoPlayOnClick ? 0 : -1}
          onKeyDown={(e) => {
            if (!autoPlayOnClick) return;
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              toggle();
            }
          }}
        >
          <video
            ref={videoRef}
            className={`block max-h-[60%] w-full object-contain ${roundedClassName}`}
            poster={poster}
            controls={controls}
            muted={muted}
            loop={loop}
            playsInline
            preload="metadata"
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          >
            {renderSources()}
          </video>

          {/* Dark mask (subtle) */}
          <div className="pointer-events-none absolute inset-0 bg-black/20" />

          {/* Play overlay */}
          <div
            className={`absolute inset-0 grid place-items-center ${
              showPlayOverlay && !isPlaying ? "opacity-100" : "opacity-0"
            }`}
          >
            <div className="flex items-center justify-center h-16 w-16 rounded-full bg-white/10 backdrop-blur-sm border border-white/50">
              {/* Play icon (SVG) */}
              <svg
                viewBox="0 0 24 24"
                aria-hidden="true"
                className="h-7 w-7 text-white"
                fill="currentColor"
              >
                <path d="M8 5v14l11-7z" />
              </svg>
            </div>
          </div>

          {/* Loading spinner (only before first canplay) */}
          {/* {isLoading && !isPlaying && (
            <div className="absolute inset-0 grid place-items-center">
              <div className="h-6 w-6 animate-spin rounded-full border-2 border-white/40 border-t-transparent" />
            </div>
          )} */}

          {/* Bottom gradient + caption (optional) */}
          {caption && (
            <figcaption className="pointer-events-none absolute inset-x-0 bottom-0 p-3 text-white">
              <div className="absolute inset-0 -z-10 bg-gradient-to-t from-black/70 to-transparent" />
              <span className="block text-sm/5 font-medium drop-shadow">
                {caption}
              </span>
            </figcaption>
          )}
        </div>
      </figure>
    );
  }
);

export default MyVideo;

// --- Example usage ---
// <MyVideo
// src=[
// { src: "/videos/demo.webm", type: "video/webm" },
// { src: "/videos/demo.mp4", type: "video/mp4" },
// ]
// poster="/images/demo-poster.jpg"
// caption="Hội cựu sinh viên DHQG"
// aspectClassName="aspect-[9/16]" // looks like your screenshot (mobile vertical)
// roundedClassName="rounded-xl"
// />
