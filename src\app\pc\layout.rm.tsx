import type { <PERSON>ada<PERSON> } from "next";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import { Roboto } from "next/font/google";
import "@/pc/globals.pc.css";
import { ViewModeProvider } from "@/contexts/ViewModeContext";
import { TabProvider } from "@/contexts/TabContext";
import { FolderProvider } from "@/contexts/FolderContext";
import { CompressedViewProvider } from "@/contexts/CompressedViewContext";
import Shell from "@/components/pc/Shell";

import HeaderPc from "@/components/pc/header/Header";
import FooterPC from "@/components/pc/header/Footer";

const roboto = Roboto({
  subsets: ["latin"], // chọn subset cần
  weight: ["400", "500", "600", "700", "800", "900"], // chọn độ đậm
  variable: "--font-roboto", // CSS variable
});

export const metadata: Metadata = {
  title: "Rocket Converter",
  description: "Convert video file fast and convenient",
};

const TOOLS = ["trim", "convert", "crop", "rotation"];

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <html lang="en">
      <body className={`${roboto.variable} antialiased`}>
        <AntdRegistry>
          <ViewModeProvider>
            <TabProvider>
              <FolderProvider>
                <CompressedViewProvider>
                  <Shell>{children}</Shell>
                </CompressedViewProvider>
              </FolderProvider>
            </TabProvider>
          </ViewModeProvider>
        </AntdRegistry>
      </body >
    </html >
  );
}
