/* CSS Module cho skill list effects, animations và layouts */

/* Base skill list container */
.skillList {
  width: 100%;
  padding: 1rem;
}

/* Grid Layout */
.gridLayout {
  display: grid;
  gap: 1rem;
  justify-items: center;
}

.columns2 {
  grid-template-columns: repeat(2, 1fr);
}

.columns3 {
  grid-template-columns: repeat(3, 1fr);
}

.columns4 {
  grid-template-columns: repeat(4, 1fr);
}

.columns5 {
  grid-template-columns: repeat(5, 1fr);
}

.columns6 {
  grid-template-columns: repeat(6, 1fr);
}

.columns7 {
  grid-template-columns: repeat(7, 1fr);
}

.columns8 {
  grid-template-columns: repeat(8, 1fr);
}

/* List Layout */
.listLayout {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Masonry Layout */
.masonryLayout {
  column-gap: 1rem;
  column-fill: balance;
}

.masonryColumns2 {
  columns: 2;
}

.masonryColumns3 {
  columns: 3;
}

.masonryColumns4 {
  columns: 4;
}

.masonryColumns5 {
  columns: 5;
}

.masonryColumns6 {
  columns: 6;
}

.masonryColumns7 {
  columns: 7;
}

.masonryColumns8 {
  columns: 8;
}

/* Animated container */
.animated {
  animation: listFadeIn 0.8s ease-out;
}

/* Animated list container */
.animatedList {
  animation: listFadeIn 0.8s ease-out;
}

@keyframes listFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

/* Individual skill item animation */
.skillItem {
  animation: skillSlideIn 0.6s ease-out both;
}

@keyframes skillSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1);
  }
}

/* Layout specific effects */
.layout-grid {
  position: relative;
}

.layout-list .skillItem {
  animation: listItemSlide 0.5s ease-out both;
}

@keyframes listItemSlide {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}

.layout-masonry {
  position: relative;
}

.layout-masonry .skillItem {
  animation: masonryFall 0.7s ease-out both;
  break-inside: avoid;
  margin-bottom: 1rem;
}

@keyframes masonryFall {
  0% {
    opacity: 0;
    transform: translateY(-50px) rotate(-5deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) rotate(0deg);
  }
}

/* Loading overlay */
.loadingOverlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: loadingSweep 2s ease-in-out infinite;
  pointer-events: none;
  opacity: 0;
}

@keyframes loadingSweep {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Hover effects for list container */
.animatedList:hover .skillItem {
  animation-play-state: paused;
}

.animatedList:hover .loadingOverlay {
  animation-duration: 1s;
}

/* Staggered animation delays */
.skillItem:nth-child(1) {
  animation-delay: 0s;
}
.skillItem:nth-child(2) {
  animation-delay: 0.1s;
}
.skillItem:nth-child(3) {
  animation-delay: 0.2s;
}
.skillItem:nth-child(4) {
  animation-delay: 0.3s;
}
.skillItem:nth-child(5) {
  animation-delay: 0.4s;
}
.skillItem:nth-child(6) {
  animation-delay: 0.5s;
}
.skillItem:nth-child(7) {
  animation-delay: 0.6s;
}
.skillItem:nth-child(8) {
  animation-delay: 0.7s;
}
.skillItem:nth-child(9) {
  animation-delay: 0.8s;
}
.skillItem:nth-child(n + 10) {
  animation-delay: 0.9s;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .skillItem {
    animation-duration: 0.4s;
  }

  .layout-masonry .skillItem {
    animation: skillSlideIn 0.4s ease-out both;
  }

  .loadingOverlay {
    animation: none;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animatedList,
  .skillItem,
  .loadingOverlay {
    animation: none;
  }
}
