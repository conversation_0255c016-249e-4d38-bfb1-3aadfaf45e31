"use client";

import React, { useEffect, useRef } from "react";
import MobileModal from "./MobileModal";
// NOTE: Adjust the import path below to where you placed MobileModal.tsx

export type EditNamePopupProps = {
  open: boolean;
  content: string;
  onClose: () => void;
  autoCloseMs?: number; // optional auto close
  className?: string; // extra classes for Modal content
};

/**
 * EditNamePopup — reuses the shared MobileModal (footerless).
 * - Displays new name, and a large rounded OK button inside modal body.
 */
export default function EditNamePopup({
  open,
  content,
  onClose,
  autoCloseMs,
  className = "",
}: EditNamePopupProps) {
  const okBtnRef = useRef<HTMLButtonElement | null>(null);

  // Auto focus OK when open
  useEffect(() => {
    if (open) okBtnRef.current?.focus();
  }, [open]);

  // Auto close after a delay (optional)
  useEffect(() => {
    if (!open || !autoCloseMs) return;
    const t = setTimeout(onClose, autoCloseMs);
    return () => clearTimeout(t);
  }, [open, autoCloseMs, onClose]);

  return (
    <MobileModal
      open={open}
      onClose={onClose}
      title={null}
      className={className}
    >
      {/* Name text */}
      <p className="text-base font-semibold text-white leading-6 line-clamp-2">
        {content}
      </p>

      {/* Action button inside body */}
      <button
        ref={okBtnRef}
        type="button"
        onClick={onClose}
        className="mt-4 w-full rounded-full bg-purple-btn hover:bg-purple-btn/25 text-white font-semibold py-3 transition focus:outline-none focus:ring-2 focus:ring-white/50"
      >
        OK
      </button>
    </MobileModal>
  );
}

// --- Example usage ---
// const [open, setOpen] = useState(false);
// <EditNamePopup open={open} name="Hội cựu sinh viên ĐH QG – edit title" onClose={() => setOpen(false)} />
