"use client";

import { useState } from "react";
import {
  CalendarOutlined,
  FileOutlined,
  SortAscendingOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import Image from "next/image";

type SortOption = {
  id: string;
  label: string;
  selected: boolean;
};

type SortCategory = {
  id: string;
  title: string;
  icon: React.ReactNode;
  options: SortOption[];
};

type Props = {
  open: boolean;
  onClose: () => void;
};

export default function SortModal({ open, onClose }: Props) {
  const [sortCategories, setSortCategories] = useState<SortCategory[]>([
    {
      id: "date",
      title: "Date",
      icon: <Image src="/assets/svg/date.svg" alt="Date" width={24} height={24} />,
      options: [
        { id: "newest", label: "Newest first", selected: true },
        { id: "oldest", label: "Oldest first", selected: false },
      ],
    },
    {
      id: "size",
      title: "File size",
      icon: <Image src="/assets/svg/save.svg" alt="Size" width={24} height={24} />,
      options: [
        { id: "biggest", label: "Biggest first", selected: false },
        { id: "smallest", label: "Smallest first", selected: false },
      ],
    },
    {
      id: "name",
      title: "File name",
      icon: <Image src="/assets/svg/filltername.svg" alt="Name" width={24} height={24} />,
      options: [
        { id: "atoz", label: "A to Z", selected: false },
        { id: "ztoa", label: "Z to A", selected: false },
      ],
    },
    {
      id: "duration",
      title: "Duration",
      icon: <Image src="/assets/svg/duration.svg" alt="Duration" width={24} height={24} />,
      options: [
        { id: "shortest", label: "Shortest first", selected: false },
        { id: "longest", label: "Longest first", selected: false },
      ],
    },
  ]);

  const handleOptionSelect = (categoryId: string, optionId: string) => {
    setSortCategories(prev =>
      prev.map(category =>
        category.id === categoryId
          ? {
              ...category,
              options: category.options.map(option => ({
                ...option,
                selected: option.id === optionId,
              })),
            }
          : category
      )
    );
  };

  if (!open) return null;

  return (
    <>
      {/* Overlay */}
      <div
        className="fixed inset-0 z-50 bg-black/50"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="w-full max-w-sm bg-[#333333] rounded-lg text-white relative" onClick={(e) => e.stopPropagation()}>
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
            aria-label="Close modal"
          >
            <svg viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
            </svg>
          </button>

          {/* Title */}
          <h2 className="font-medium text-2xl mb-6 mt-4 ml-4">Sort by</h2>

          {/* Sort Categories */}
          <div className="space-y-0">
            {sortCategories.map((category) => (
              <div key={category.id} className="mb-4">
                {/* Category Header */}
                <div className="w-full h-12 bg-[#1F1F1F] flex px-4 items-center gap-3 mb-2 ">
                  <span className="text-gray-300 text-lg">{category.icon}</span>
                  <span className="font-semibold text-xl">{category.title}</span>
                </div>

                {/* Options */}
                <div className="ml-12 space-y-4">
                  {category.options.map((option) => (
                    <label
                      key={option.id}
                      className="flex items-center justify-between cursor-pointer py-1"
                    >
                      <span className="text-lg text-gray-200">{option.label}</span>
                      <div className="relative mr-9">
                        <input
                          type="radio"
                          name={category.id}
                          checked={option.selected}
                          onChange={() => handleOptionSelect(category.id, option.id)}
                          className="sr-only"
                        />
                        <div
                          className={`w-5 h-5 border-2 border-gray-400 rounded-full flex items-center justify-center ${
                            option.selected ? "border-white bg-white" : "bg-transparent"
                          }`}
                        >
                          {option.selected && (
                            <div className="w-2 h-2 bg-white rounded-full" />
                          )}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
