"use client";

import { createContext, useContext, useState, ReactNode } from "react";

type CompressedViewMode = "grid" | "list";

interface CompressedViewContextType {
  compressedViewMode: CompressedViewMode;
  setCompressedViewMode: (mode: CompressedViewMode) => void;
}

const CompressedViewContext = createContext<CompressedViewContextType | undefined>(undefined);

export function CompressedViewProvider({ children }: { children: ReactNode }) {
  const [compressedViewMode, setCompressedViewMode] = useState<CompressedViewMode>("grid");

  return (
    <CompressedViewContext.Provider value={{ compressedViewMode, setCompressedViewMode }}>
      {children}
    </CompressedViewContext.Provider>
  );
}

export function useCompressedView() {
  const context = useContext(CompressedViewContext);
  if (context === undefined) {
    throw new Error("useCompressedView must be used within a CompressedViewProvider");
  }
  return context;
}
