"use client";
import React, { useState } from "react";
import { Upload, Button } from "antd";
import type { UploadFile, UploadProps } from "antd";
import { DownOutlined, UploadOutlined } from "@ant-design/icons";
import PillButton from "./PillButton";

const { Dragger } = Upload;

type Props = {
  /** Nhận danh sách File gốc khi người dùng chọn/kéo thả */
  onFilesChange?: (files: File[]) => void;
  /** MIME types cho input */
  accept?: string;
  /** Số file tối đa */
  maxCount?: number;
};

const DragUploadBox: React.FC<Props> = ({
  onFilesChange,
  accept = "video/*,image/*,audio/*",
  maxCount = 20,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const draggerProps: UploadProps = {
    multiple: true,
    accept,
    maxCount,
    fileList,
    // Không upload lên server ngay; giữ file ở client
    beforeUpload: () => false,
    onChange(info) {
      setFileList(info.fileList);
      const files = info.fileList
        .map((f) => f.originFileObj)
        .filter(Boolean) as File[];
      onFilesChange?.(files);
    },
    onDrop() {
      // có thể log hoặc hiển thị thông báo nếu muốn
    },
  };

  return (
    <div className="mx-auto mt-6 md:mt-10 w-content">
      <Dragger
        {...draggerProps}
        className="[&>div]:!bg-white !rounded-2xl !border-dashed !border-gray-300 !p-0 shadow-sm"
      >
        <div className="py-4">
          <div className="flex items-center justify-center">
            <PillButton />
          </div>
          <p className="mt-2 text-center text-xl font-bold text-black">
            Select file or drag and drop here to start
          </p>
        </div>
      </Dragger>
    </div>
  );
};

export default DragUploadBox;
