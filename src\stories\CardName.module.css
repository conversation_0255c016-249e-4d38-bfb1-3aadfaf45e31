/* CSS Module chỉ cho text effects và animations đặc biệt */

/* Animated name với subtle movement */
.animatedName {
  animation: nameFloat 4s ease-in-out infinite;
}

@keyframes nameFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-1px);
  }
}

.animatedSubtitle {
  animation: subtitleFade 3s ease-in-out infinite alternate;
}

@keyframes subtitleFade {
  0% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

/* Rarity specific text effects */
.rarity-rare {
  text-shadow: 0 0 2px rgba(59, 130, 246, 0.3);
}

.rarity-epic {
  text-shadow: 0 0 3px rgba(139, 92, 246, 0.4);
}

.rarity-legendary {
  background: linear-gradient(45deg, #fbbf24, #f59e0b, #fbbf24);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: legendaryTextShine 3s ease-in-out infinite;
}

@keyframes legendaryTextShine {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Legendary text glow overlay */
.textGlow {
  position: absolute;
  inset: 0;
  background: radial-gradient(
    ellipse at center,
    rgba(251, 191, 36, 0.2) 0%,
    transparent 70%
  );
  animation: glowPulse 2s ease-in-out infinite alternate;
  pointer-events: none;
}

@keyframes glowPulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Crown icon animation */
.crownIcon {
  display: inline-block;
  animation: crownBounce 2s ease-in-out infinite;
}

@keyframes crownBounce {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-2px) rotate(-5deg);
  }
  75% {
    transform: translateY(-1px) rotate(5deg);
  }
}

/* Hover effects */
.animatedName:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

.rarity-legendary:hover .crownIcon {
  animation-duration: 0.5s;
}

/* Text selection styling */
.rarity-legendary::selection {
  background: rgba(251, 191, 36, 0.3);
}

.rarity-epic::selection {
  background: rgba(139, 92, 246, 0.3);
}

.rarity-rare::selection {
  background: rgba(59, 130, 246, 0.3);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .animatedName {
    font-size: 1rem;
  }

  .crownIcon {
    font-size: 0.8em;
  }
}
