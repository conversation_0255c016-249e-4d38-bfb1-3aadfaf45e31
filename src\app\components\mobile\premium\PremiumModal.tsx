"use client";

import { useState } from "react";
import Image from "next/image";

type PremiumModalProps = {
  open: boolean;
  onClose: () => void;
};

const features = [
  {
    name: "Batch compression",
    free: "3 video",
    premium: "Unlimited",
    available: false,
  },
  {
    name: "No ads",
    free: false,
    premium: true,
    available: false,
  },
  {
    name: "Keep original metadata",
    free: false,
    premium: true,
    available: false,
  },
  {
    name: "Video editing",
    free: false,
    premium: true,
    available: false,
  },
  {
    name: "No promo text",
    free: false,
    premium: true,
    available: false,
  },
  {
    name: "Email support",
    free: false,
    premium: true,
    available: false,
  },
  {
    name: "Support future development",
    free: false,
    premium: true,
    available: false,
  },
];

export default function PremiumModal({ open, onClose }: PremiumModalProps) {
  const [selectedPlan, setSelectedPlan] = useState<
    "3months" | "1year" | "lifetime"
  >("1year");

  if (!open) return null;

  return (
    <>
      {/* Modal */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="w-full max-w-md bg-[#0D0D0D] rounded-lg text-white relative">
          {/* Close Button */}
          <button onClick={onClose} className=" w-8 h-8 text-white">
            x
          </button>

          <div className="p-4 pt-8">
            {/* App Icon */}
            <div className="flex justify-center mb-[2px] mt-8">
              <div className="relative w-24 h-24">
                <Image
                  src="/file.svg"
                  alt="Fast Converter Logo"
                  fill
                  objectFit="cover"
                />
              </div>
            </div>

            {/* Title */}
            <h2 className="font-medium text-2xl leading-[100%] tracking-[0] text-center text-primary mb-4">
              Upgrade to Premium
            </h2>

            {/* Features Table */}
            <div className="mb-6 ">
              <div className="grid grid-cols-3 gap-2 mb-4 bg-darkbackground font-medium text-sm leading-[100%] tracking-[0]">
                <div className="py-2 px-1">Features</div>
                <div className="text-center py-2 px-1">Free</div>
                <div className="text-center py-2 px-1">Premium</div>
              </div>

              {features.map((feature, index) => (
                <div
                  key={index}
                  className="grid grid-cols-3 gap-2 py-2 border-b border-gray-700 last:border-b-0"
                >
                  <div className="font-normal text-sm leading-[20px] tracking-[0] w-max">
                    {feature.name}
                  </div>
                  <div className="text-center font-normal text-sm leading-[20px] tracking-[0]">
                    {typeof feature.free === "string" ? (
                      <span className="text-sm text-white">{feature.free}</span>
                    ) : feature.free ? (
                      <svg
                        className="w-4 h-4 mx-auto text-green-500"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="w-4 h-4 mx-auto text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </div>
                  <div className="text-center font-normal text-sm leading-[20px] tracking-[0]">
                    {typeof feature.premium === "string" ? (
                      <span className="text-sm text-primary">
                        {feature.premium}
                      </span>
                    ) : feature.premium ? (
                      <svg
                        className="w-4 h-4 mx-auto text-primary"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="w-4 h-4 mx-auto text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Subscriptions */}
            <div className="mb-4">
                          <h3 className="text-sm leading-[20px] tracking-[0] text-[#969494] mb-2">
              Subscriptions
            </h3>

              {/* 3 Months */}
              <button
                onClick={() => setSelectedPlan("3months")}
                className={`w-full rounded-[50px]  flex justify-between 
            p-[5px] pr-[15px] pb-[5px] pl-[15px] border mb-3 ${
              selectedPlan === "3months"
                ? "border-primary bg-primary/10"
                : "border-gray-600 bg-gray-800"
            } flex items-center justify-between`}
              >
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-primary rounded flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-black"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                  <span className="text-sm leading-[20px] ">3 MONTHS</span>
                </div>
                                  <div className="text-right">
                    <div className="text-lg leading-[20px]">49.999 đ</div>
                    <div className="text-xs leading-[20px] text-[#969494]">
                      10.000 đ / months
                    </div>
                  </div>
                </button>

                {/* 1 Year - Popular */}
                <div className="relative">
                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-primary text-white text-xs px-2 py-1 rounded">
                    Popular
                  </div>
                  <button
                    onClick={() => setSelectedPlan("1year")}
                    className={`w-full rounded-[50px]  flex justify-between 
            p-[5px] pr-[15px] pb-[5px] pl-[15px] border mb-3 bg-primary ${
              selectedPlan === "1year"
                ? "border-primary bg-primary"
                : "border-gray-600 bg-primary/10"
            } flex items-center justify-between`}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-primary rounded flex items-center justify-center">
                        <svg
                          className="w-4 h-4 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      </div>
                      <span className="text-sm leading-[20px] ">1 YEARS</span>
                    </div>
                    <div className="text-right">
                      <div className="text-lg leading-[20px]">99.999 đ</div>
                      <div className="text-xs leading-[20px] text-[#969494]">
                        8.000 đ / months
                      </div>
                    </div>
                  </button>
                </div>

              {/* One-time purchase */}
              <div className="mb-3">
                <h4 className="text-sm leading-[20px] tracking-[0] text-[#969494] mb-2">
                  One-time purchase
                </h4>
                <button
                  onClick={() => setSelectedPlan("lifetime")}
                  className={`w-full rounded-[50px]  flex justify-between 
            p-[5px] pr-[15px] pb-[5px] pl-[15px] border mb-3 bg-primary ${
              selectedPlan === "lifetime"
                ? "border-primary bg-primary/10"
                : "border-gray-600 bg-gray-800"
            } flex items-center justify-between`}
                >
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-primary rounded flex items-center justify-center">
                      <svg
                        className="w-4 h-4 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    </div>
                    <span className="text-sm leading-[20px] ">
                      LIFETIME
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-lg leading-[20px]">399.999 đ</div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
