generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Contact {
  id           Int       @id @default(autoincrement())
  created_by   Int
  title        String
  message      String
  created_at   DateTime?
  updated_at   DateTime?
  deleted_at   DateTime?
  name         String?
  email        String?
  phone_number String?
  is_active    Int       @default(1)

  @@map("contacts")
}

model User {
  id                Int       @id @default(autoincrement())
  first_name        String?
  last_name         String?
  code              String?   @unique
  email             String    @unique
  phone_number      String?   @unique
  sex               Boolean   @default(true)
  password          String
  birthday          DateTime?
  address           String?
  avatar            String?
  remember_token    String?
  is_active         Boolean   @default(true)
  last_login        DateTime?
  last_logout       DateTime?
  slack_webhook_url String?
  created_at        DateTime?
  updated_at        DateTime?
  deleted_at        DateTime?
  coin              BigInt    @default(1000)
  locale            String?   @default("en")
  group_id          Int?      @default(1)

  @@map("users")
}

model WordType {
  id         Int       @id @default(autoincrement())
  name       String
  created_at DateTime?
  updated_at DateTime?

  @@map("word_types")
}

model PasswordReset {
  email      String
  token      String
  created_at DateTime?

  @@id([email, token])
  @@map("password_resets")
}

model Subject {
  id          Int       @id @default(autoincrement())
  name        String
  description String
  is_active   Int
  created_at  DateTime?
  updated_at  DateTime?

  @@map("subjects")
}

model UserProfile {
  id          Int       @id @default(autoincrement())
  user_id     Int
  height      Int?
  weight      Int?
  round_one   Int?
  round_two   Int?
  round_three Int?
  image_one   String?
  image_two   String?
  image_three String?
  website     String?
  hobby       String?
  slogan      String?
  created_at  DateTime?
  updated_at  DateTime?

  @@map("user_profiles")
}

model Question {
  id         Int       @id @default(autoincrement())
  question   String
  score      Int
  type       String
  created_at DateTime?
  updated_at DateTime?
  deleted_at DateTime?

  @@map("questions")
}

model Tag {
  id         Int       @id @default(autoincrement())
  name       String
  is_active  Int       @default(0)
  created_at DateTime?
  updated_at DateTime?
  deleted_at DateTime?

  @@map("tags")
}

model Story {
  id        String   @id @default(cuid())
  prompt    String
  content   String
  createdAt DateTime @default(now())
}
