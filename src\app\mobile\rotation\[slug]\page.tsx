"use client";
import Image from "next/image";
import Link from "next/link";
import { useState, useRef, useEffect, use } from "react";
import SavingOverlay from "../../../components/mobile/video/SavingOverlay";

type VideoData = {
  id: string;
  title: string;
  thumbnail: string;
  duration: number; 
  size: string;
  url?: string; // video file URL
  createdAt?: string;
};


const mockVideoData: Record<string, VideoData> = {
  "1": {
    id: "1",
    title: "Survival in the deep forest",
    thumbnail: "/assets/images/video1.png",
    duration: 25, // 25 seconds
    size: "32MB",
    url: "/assets/videos/demo.mp4",
    createdAt: "2024-01-15",
  },
  "2": {
    id: "2",
    title: "<PERSON><PERSON><PERSON> tướng <PERSON>ê<PERSON>",
    thumbnail: "/assets/images/Video2.png",
    duration: 15, // 15 seconds
    size: "45MB",
    url: "/videos/video2.mp4",
    createdAt: "2024-01-14",
  },
  "3": {
    id: "3",
    title: "<PERSON><PERSON> quái công an",
    thumbnail: "/assets/images/Video3.png",
    duration: 30, // 30 seconds
    size: "38MB",
    url: "/videos/video3.mp4",
    createdAt: "2024-01-13",
  },
  "4": {
    id: "4",
    title: "Nữ Đại Úy",
    thumbnail: "/assets/images/Video4.png",
    duration: 22, // 22 seconds
    size: "41MB",
    url: "/videos/video4.mp4",
    createdAt: "2024-01-12",
  },
  "5": {
    id: "5",
    title: "Kaity Nguyễn đóng tiếp viên hàng không",
    thumbnail: "/assets/images/Video5.png",
    duration: 18, // 18 seconds
    size: "35MB",
    url: "/videos/video5.mp4",
    createdAt: "2024-01-11",
  },
};

type Props = { params: Promise<{ slug: string }> };

export default function RotationPage({ params }: Props) {
  const { slug } = use(params);
  const [videoData, setVideoData] = useState<VideoData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [rotation, setRotation] = useState(0); // rotation angle in degrees (0, 90, 180, 270)
  const [timelinePosition, setTimelinePosition] = useState(0); // 0-1
  const [currentTime, setCurrentTime] = useState(0); // current time in seconds
  const [isPlaying, setIsPlaying] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [simulatedTime, setSimulatedTime] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [currentFrame, setCurrentFrame] = useState<string | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Capture current video frame
  const captureCurrentFrame = () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) return;

    try {
      // Set canvas size to match video
      canvas.width = video.videoWidth || video.clientWidth;
      canvas.height = video.videoHeight || video.clientHeight;

      // Draw current video frame to canvas
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert to data URL
      const frameDataUrl = canvas.toDataURL('image/png');
      setCurrentFrame(frameDataUrl);
      console.log("Frame captured successfully");
    } catch (error) {
      console.log("Failed to capture frame:", error);
    }
  };

  // Force video to load when component mounts
  useEffect(() => {
    if (videoRef.current && videoData?.url) {
      console.log("Loading video:", videoData.url);
      videoRef.current.load();
      
      // Capture first frame when video loads
      const handleLoadedData = () => {
        setTimeout(() => captureCurrentFrame(), 500);
      };
      
      videoRef.current.addEventListener('loadeddata', handleLoadedData);
      
      return () => {
        if (videoRef.current) {
          videoRef.current.removeEventListener('loadeddata', handleLoadedData);
        }
      };
    }
  }, [videoData?.url]);

  useEffect(() => {
    const fetchVideoData = async () => {
      setLoading(true);
      setError(null);

      try {
        await new Promise((resolve) => setTimeout(resolve, 500));

        const data = mockVideoData[slug];

        if (data) {
          setVideoData(data);
        } else {
          setError(`Video not found for ID: ${slug}`);
        }
      } catch (error) {
        console.error("Error fetching video data:", error);
        setError("Failed to load video data");
      } finally {
        setLoading(false);
      }
    };

    fetchVideoData();
  }, [slug]);

  
  useEffect(() => {
    if (videoData && videoData.duration > 0) {
      setTimelinePosition(currentTime / videoData.duration);
    }
  }, [currentTime, videoData]);

  // Separate effect for simulated time
  useEffect(() => {
    if (videoData && simulatedTime > 0) {
      setCurrentTime(simulatedTime);
    }
  }, [simulatedTime, videoData]);

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const handleTimeUpdate = () => {
    if (videoRef.current && videoData) {
      const time = videoRef.current.currentTime;
      setCurrentTime(time);
    }
  };

  const togglePlayPause = async () => {
    if (!videoData || !videoRef.current) return;

    if (isPlaying) {
      // Pause
      videoRef.current.pause();
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      
      // Capture current frame when pausing
      setTimeout(() => captureCurrentFrame(), 100);
      
      setIsPlaying(false);
    } else {
      // Play
      try {
        console.log("Attempting to play video...", videoRef.current.readyState);
        
        // Force load the video if needed
        if (videoRef.current.readyState < 2) {
          videoRef.current.load();
          await new Promise(resolve => {
            videoRef.current!.addEventListener('loadeddata', resolve, { once: true });
          });
        }
        
        // Try to play
        const playPromise = videoRef.current.play();
        
        if (playPromise !== undefined) {
          await playPromise;
          console.log("Video started playing successfully");
          setIsPlaying(true);
        }
      } catch (error) {
        console.log("Video play failed:", error);
        // Fallback to simulated playback
        intervalRef.current = setInterval(() => {
          setSimulatedTime((prev) => {
            const newTime = prev + 0.1;
            if (newTime >= videoData.duration) {
              setIsPlaying(false);
              if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
              }
              return 0;
            }
            return newTime;
          });
        }, 100);
        setIsPlaying(true);
      }
    }
  };

  const rotateLeft = () => {
    setRotation((prev) => (prev - 90 + 360) % 360);
  };

  const rotateRight = () => {
    setRotation((prev) => (prev + 90) % 360);
  };

  const handleSave = async () => {
    if (!videoData) return;

    const rotationData = {
      videoId: videoData.id,
      rotation: rotation, 
      timeline: {
        currentTime: currentTime,
        duration: videoData.duration,
        position: timelinePosition,
      },
      timestamp: new Date().toISOString(),
    };

    try {
      setIsSaving(true);
      console.log("Saving rotation data:", rotationData);
    } catch (error) {
      console.error("Save error:", error);
      setIsSaving(false);
      alert("Failed to save rotation. Please try again.");
    }
  };

  const handleSaveComplete = () => {
    setIsSaving(false);
  };

  const handleSaveCancel = () => {
    setIsSaving(false);
  };

  if (loading) {
    return (
      <main className="min-h-screen bg-black text-white flex flex-col items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        <p className="mt-4 text-white/70">Loading video...</p>
      </main>
    );
  }

  if (error || !videoData) {
    return (
      <main className="min-h-screen bg-black text-white flex flex-col items-center justify-center">
        <p className="text-red-400">{error || "Video not found"}</p>
        <Link href="/mobile" className="mt-4 text-blue-400 underline">
          Go back
        </Link>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-black text-white flex flex-col">
      {/* Header */}
      <header className="sticky top-0 z-40 bg-gradient-to-b from-primary-2 to-primary">
        <div className="px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-6">
            <Link
              href="/mobile"
              className="inline-flex h-8 w-8 items-center justify-center rounded-full hover:bg-white/10"
              aria-label="Back"
            >
              <Image
                src="/assets/svg/back.svg"
                alt="Back"
                width={19}
                height={19}
              />
            </Link>
            <h1 className="text-lg font-medium">Rotation</h1>
          </div>
          
          {/* Rotation Controls */}
          
        </div>
      </header>

      {/* Video Player Area */}
      <section className="flex-1 bg-black relative overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="relative w-full h-full max-w-xs mx-auto">
            
            <div className="absolute inset-0">
              {/* Thumbnail/Current Frame - only shows when not playing */}
              {!isPlaying && (
                <div
                  className="w-full h-full bg-cover bg-center transition-transform duration-300 ease-in-out"
                  style={{ 
                    backgroundImage: `url('${currentFrame || videoData.thumbnail}')`,
                    transform: `rotate(${rotation}deg)`,
                    transformOrigin: 'center center'
                  }}
                >
                  <div className="absolute inset-0 bg-black/20" />
                </div>
              )}

              {/* Video element - always visible, rotates with video */}
              {videoData.url && (
                <video
                  ref={videoRef}
                  className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 ease-in-out cursor-pointer"
                  style={{
                    transform: `rotate(${rotation}deg)`,
                    transformOrigin: 'center center',
                    opacity: isPlaying ? 1 : 0
                  }}
                  onClick={togglePlayPause}
                  onTimeUpdate={handleTimeUpdate}
                  onPlay={() => {
                    console.log("Video onPlay event fired");
                    setIsPlaying(true);
                  }}
                  onPause={() => {
                    console.log("Video onPause event fired");
                    setIsPlaying(false);
                  }}
                  onError={(e) => {
                    console.log("Video error:", e);
                    setIsPlaying(false);
                  }}
                  onLoadedData={() => {
                    console.log("Video loaded successfully - ready to play");
                  }}
                  onCanPlay={() => {
                    console.log("Video can start playing");
                  }}
                  preload="auto"
                  muted
                  playsInline
                  controls={false}
                  autoPlay={false}
                >
                  <source src={videoData.url} type="video/mp4" />
                </video>
              )}

              {/* Play/Pause Button - overlay */}
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <button
                  onClick={togglePlayPause}
                  className="w-16 h-16 bg-black/40 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-black/60 transition-colors pointer-events-auto"
                >
                  {isPlaying ? (
                    <svg width="28" height="28" viewBox="0 0 24 24" fill="white">
                      <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                    </svg>
                  ) : (
                    <svg width="28" height="28" viewBox="0 0 24 24" fill="white" style={{ marginLeft: '2px' }}>
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {/* Hidden canvas for frame capture */}
        <canvas
          ref={canvasRef}
          style={{ display: 'none' }}
        />
      </section>

      {/* Bottom Controls Panel */}
      <section className="p-4 space-y-4 bg-black mt-12 mb-14">
        {/* Timeline */}
        <div className="space-y-2 mb-6">
          <div className="relative">
            <div 
              className="w-full h-1 bg-neutral-600 rounded-full cursor-pointer"
              onClick={(e) => {
                if (!videoData) return;
                const rect = e.currentTarget.getBoundingClientRect();
                const clickX = e.clientX - rect.left;
                const newPosition = Math.max(0, Math.min(1, clickX / rect.width));
                const newTime = newPosition * videoData.duration;
                
                setCurrentTime(newTime);
                setSimulatedTime(newTime);
                setTimelinePosition(newPosition);
                
                if (videoRef.current) {
                  videoRef.current.currentTime = newTime;
                  
                  // Capture frame after seeking (if paused)
                  if (!isPlaying) {
                    setTimeout(() => captureCurrentFrame(), 200);
                  }
                }
              }}
            >
              <div
                className="h-full bg-white rounded-full transition-all duration-200"
                style={{ width: `${timelinePosition * 100}%` }}
              />
            </div>
            <div
              className="absolute top-1/2 w-3 h-3 bg-white rounded-full -translate-y-1/2 transition-all duration-200"
              style={{ left: `${timelinePosition * 100}%`, marginLeft: "-6px" }}
            />
          </div>

          {/* Time Indicators */}
          <div className="flex justify-between text-sm text-white/80">
            <span>{Math.floor(currentTime / 60).toString().padStart(2, '0')}:{Math.floor(currentTime % 60).toString().padStart(2, '0')}</span>
            <span>{Math.floor(videoData.duration / 60).toString().padStart(2, '0')}:{Math.floor(videoData.duration % 60).toString().padStart(2, '0')}</span>
          </div>
        </div>

        {/* Rotation Controls */}
        <div className="flex items-center gap-3 justify-center ">
            <button
              onClick={rotateLeft}
              className="flex items-center justify-center  rounded-md gap-2  px-4 py-3 bg-[#2C2C2C]"
              aria-label="Rotate Left"
            >
              <Image
                src="/assets/svg/rotateleft.svg"
                alt="Rotate Left"
                width={20}
                height={20}
              />
              <p className="text-white font-medium" >Rotate Left</p>
            </button>
            <button
              onClick={rotateRight}
              className="flex items-center justify-center  rounded-md gap-2  px-4 py-3 bg-[#2C2C2C]"
              aria-label="Rotate Right"
            >
              <Image
                src="/assets/svg/rorateright.svg"
                alt="Rotate Right"
                width={20}
                height={20}
              />
              <p className="text-white font-medium" >Rotate Right</p>
            </button>
          </div>
      </section>

      {/* Save Button */}
      <footer className="sticky bottom-0">
        <button
          className="w-full bg-primary hover:bg-primary-2 text-white text-center py-4.5 font-medium text-lg transition-colors"
          onClick={handleSave}
        >
          SAVE
        </button>
      </footer>

      {/* Saving Overlay */}
      {isSaving && (
        <SavingOverlay
          videoThumbnail={videoData.thumbnail}
          onCancel={handleSaveCancel}
          onComplete={handleSaveComplete}
        />
      )}
    </main>
  );
}
