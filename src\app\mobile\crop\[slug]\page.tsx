"use client";
import Image from "next/image";
import Link from "next/link";
import { useState, useRef, useEffect, use } from "react";
import SavingOverlay from "../../../components/mobile/video/SavingOverlay";

type VideoData = {
  id: string;
  title: string;
  thumbnail: string;
  duration: number; // duration in seconds
  size: string;
  url?: string; // video file URL
  createdAt?: string;
};

// Mock API data - replace with real API call
const mockVideoData: Record<string, VideoData> = {
  "1": {
    id: "1",
    title: "Survival in the deep forest",
    thumbnail: "/assets/images/Video1.png",
    duration: 25, // 25 seconds
    size: "32MB",
    url: "/videos/video1.mp4",
    createdAt: "2024-01-15",
  },
  "2": {
    id: "2",
    title: "Đ<PERSON>i tướng <PERSON>",
    thumbnail: "/assets/images/Video2.png",
    duration: 15, // 15 seconds
    size: "45MB",
    url: "/videos/video2.mp4",
    createdAt: "2024-01-14",
  },
  "3": {
    id: "3",
    title: "Nữ quái công an",
    thumbnail: "/assets/images/Video3.png",
    duration: 30, // 30 seconds
    size: "38MB",
    url: "/videos/video3.mp4",
    createdAt: "2024-01-13",
  },
  "4": {
    id: "4",
    title: "Nữ Đại Úy",
    thumbnail: "/assets/images/Video4.png",
    duration: 22, // 22 seconds
    size: "41MB",
    url: "/videos/video4.mp4",
    createdAt: "2024-01-12",
  },
  "5": {
    id: "5",
    title: "Kaity Nguyễn đóng tiếp viên hàng không",
    thumbnail: "/assets/images/Video5.png",
    duration: 18, // 18 seconds
    size: "35MB",
    url: "/videos/video5.mp4",
    createdAt: "2024-01-11",
  },
};

type Props = { params: Promise<{ slug: string }> };

export default function CropPage({ params }: Props) {
  const { slug } = use(params);
  const [videoData, setVideoData] = useState<VideoData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cropArea, setCropArea] = useState({
    x: 50,
    y: 50,
    width: 200,
    height: 300,
  });
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [resizeHandle, setResizeHandle] = useState<string>("");
  const [selectedRatio, setSelectedRatio] = useState("custom");
  const [timelinePosition, setTimelinePosition] = useState(0); // 0-1
  const [currentTime, setCurrentTime] = useState(0); // current time in seconds
  const [isPlaying, setIsPlaying] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const cropRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  const getEventCoordinates = (e: React.MouseEvent | React.TouchEvent) => {
    if ("touches" in e) {
      return { clientX: e.touches[0].clientX, clientY: e.touches[0].clientY };
    }
    return { clientX: e.clientX, clientY: e.clientY };
  };

  const handleMouseDown = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
    setIsActive(true);
    const { clientX, clientY } = getEventCoordinates(e);
    const rect = containerRef.current?.getBoundingClientRect();
    if (rect) {
      const startPos = {
        x: clientX - rect.left - cropArea.x,
        y: clientY - rect.top - cropArea.y,
      };
      setDragStart(startPos);
    }
  };

  const handleResizeMouseDown = (
    e: React.MouseEvent | React.TouchEvent,
    handle: string
  ) => {
    e.stopPropagation();
    e.preventDefault();
    setIsResizing(true);
    setIsActive(true);
    setResizeHandle(handle);
    const { clientX, clientY } = getEventCoordinates(e);
    const rect = containerRef.current?.getBoundingClientRect();
    if (rect) {
      setDragStart({
        x: clientX - rect.left,
        y: clientY - rect.top,
      });
    }
  };

  // Global mouse and touch event handlers
  useEffect(() => {
    const getCoordinates = (e: MouseEvent | TouchEvent) => {
      if ("touches" in e) {
        return { clientX: e.touches[0].clientX, clientY: e.touches[0].clientY };
      }
      return { clientX: e.clientX, clientY: e.clientY };
    };

    const handleGlobalMove = (e: MouseEvent | TouchEvent) => {
      if (!isDragging && !isResizing) return;

      e.preventDefault();
      const rect = containerRef.current?.getBoundingClientRect();
      if (!rect) return;

      const { clientX, clientY } = getCoordinates(e);
      const mouseX = clientX - rect.left;
      const mouseY = clientY - rect.top;

      if (isDragging) {
        // Get image container bounds (max-w-xs container)
        const imageContainer =
          rect.width > rect.height
            ? { width: Math.min(rect.width, 320), height: rect.height } // max-w-xs = 320px
            : { width: rect.width, height: rect.height };

        // Calculate image area center offset
        const imageX = (rect.width - imageContainer.width) / 2;
        const imageY = (rect.height - imageContainer.height) / 2;

        // Constrain movement within image bounds
        const newX = Math.max(
          imageX,
          Math.min(
            mouseX - dragStart.x,
            imageX + imageContainer.width - cropArea.width
          )
        );
        const newY = Math.max(
          imageY,
          Math.min(
            mouseY - dragStart.y,
            imageY + imageContainer.height - cropArea.height
          )
        );

        setCropArea((prev) => ({ ...prev, x: newX, y: newY }));
      } else if (isResizing) {
        setCropArea((prev) => {
          let newArea = { ...prev };
          const minSize = 50;

          // Get image container bounds (same logic as dragging)
          const imageContainer =
            rect.width > rect.height
              ? { width: Math.min(rect.width, 320), height: rect.height }
              : { width: rect.width, height: rect.height };

          const imageX = (rect.width - imageContainer.width) / 2;
          const imageY = (rect.height - imageContainer.height) / 2;
          const imageRight = imageX + imageContainer.width;
          const imageBottom = imageY + imageContainer.height;

          switch (resizeHandle) {
            case "top-left":
              const newWidth1 = Math.max(
                minSize,
                prev.x + prev.width - Math.max(mouseX, imageX)
              );
              const newHeight1 = Math.max(
                minSize,
                prev.y + prev.height - Math.max(mouseY, imageY)
              );
              newArea.x = prev.x + prev.width - newWidth1;
              newArea.y = prev.y + prev.height - newHeight1;
              newArea.width = newWidth1;
              newArea.height = newHeight1;
              break;
            case "top-right":
              newArea.width = Math.max(
                minSize,
                Math.min(imageRight - prev.x, mouseX - prev.x)
              );
              const newHeight2 = Math.max(
                minSize,
                prev.y + prev.height - Math.max(mouseY, imageY)
              );
              newArea.y = prev.y + prev.height - newHeight2;
              newArea.height = newHeight2;
              break;
            case "bottom-left":
              const newWidth3 = Math.max(
                minSize,
                prev.x + prev.width - Math.max(mouseX, imageX)
              );
              newArea.x = prev.x + prev.width - newWidth3;
              newArea.width = newWidth3;
              newArea.height = Math.max(
                minSize,
                Math.min(imageBottom - prev.y, mouseY - prev.y)
              );
              break;
            case "bottom-right":
              newArea.width = Math.max(
                minSize,
                Math.min(imageRight - prev.x, mouseX - prev.x)
              );
              newArea.height = Math.max(
                minSize,
                Math.min(imageBottom - prev.y, mouseY - prev.y)
              );
              break;
            case "top":
              const newHeight4 = Math.max(
                minSize,
                prev.y + prev.height - Math.max(mouseY, imageY)
              );
              newArea.y = prev.y + prev.height - newHeight4;
              newArea.height = newHeight4;
              break;
            case "bottom":
              newArea.height = Math.max(
                minSize,
                Math.min(imageBottom - prev.y, mouseY - prev.y)
              );
              break;
            case "left":
              const newWidth4 = Math.max(
                minSize,
                prev.x + prev.width - Math.max(mouseX, imageX)
              );
              newArea.x = prev.x + prev.width - newWidth4;
              newArea.width = newWidth4;
              break;
            case "right":
              newArea.width = Math.max(
                minSize,
                Math.min(imageRight - prev.x, mouseX - prev.x)
              );
              break;
          }

          // Apply aspect ratio if not custom
          if (selectedRatio !== "custom") {
            const aspectRatio = getAspectRatio(selectedRatio);
            if (aspectRatio) {
              if (
                resizeHandle.includes("right") ||
                resizeHandle.includes("left")
              ) {
                newArea.height = newArea.width / aspectRatio;
              } else {
                newArea.width = newArea.height * aspectRatio;
              }
            }
          }

          return newArea;
        });
      }
    };

    const handleGlobalEnd = () => {
      setIsDragging(false);
      setIsResizing(false);
      setResizeHandle("");
      setIsActive(false);
    };

    if (isDragging || isResizing) {
      // Mouse events
      document.addEventListener("mousemove", handleGlobalMove);
      document.addEventListener("mouseup", handleGlobalEnd);
      // Touch events
      document.addEventListener("touchmove", handleGlobalMove, {
        passive: false,
      });
      document.addEventListener("touchend", handleGlobalEnd);
      document.addEventListener("touchcancel", handleGlobalEnd);

      document.body.style.userSelect = "none";
      document.body.style.touchAction = "none";
      document.body.style.cursor = isResizing
        ? `${resizeHandle}-resize`
        : "move";
    }

    return () => {
      // Mouse events
      document.removeEventListener("mousemove", handleGlobalMove);
      document.removeEventListener("mouseup", handleGlobalEnd);
      // Touch events
      document.removeEventListener("touchmove", handleGlobalMove);
      document.removeEventListener("touchend", handleGlobalEnd);
      document.removeEventListener("touchcancel", handleGlobalEnd);

      document.body.style.userSelect = "";
      document.body.style.touchAction = "";
      document.body.style.cursor = "";
    };
  }, [
    isDragging,
    isResizing,
    dragStart,
    cropArea,
    resizeHandle,
    selectedRatio,
  ]);

  // Fetch video data based on slug
  useEffect(() => {
    const fetchVideoData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Simulate API call delay
        await new Promise((resolve) => setTimeout(resolve, 500));

        // In real app, replace with: const response = await fetch(`/api/videos/${slug}`);
        const data = mockVideoData[slug];

        if (data) {
          setVideoData(data);
        } else {
          setError(`Video not found for ID: ${slug}`);
        }
      } catch (error) {
        console.error("Error fetching video data:", error);
        setError("Failed to load video data");
      } finally {
        setLoading(false);
      }
    };

    fetchVideoData();
  }, [slug]);

  // Initialize crop position within image bounds
  useEffect(() => {
    const initializeCrop = () => {
      const containerRect = containerRef.current?.getBoundingClientRect();
      if (!containerRect) return;

      const imageContainer =
        containerRect.width > containerRect.height
          ? {
              width: Math.min(containerRect.width, 320),
              height: containerRect.height,
            }
          : { width: containerRect.width, height: containerRect.height };

      const imageX = (containerRect.width - imageContainer.width) / 2;
      const imageY = (containerRect.height - imageContainer.height) / 2;

      setCropArea((prev) => ({
        ...prev,
        x: Math.max(
          imageX,
          Math.min(prev.x, imageX + imageContainer.width - prev.width)
        ),
        y: Math.max(
          imageY,
          Math.min(prev.y, imageY + imageContainer.height - prev.height)
        ),
      }));
    };

    // Initialize after a short delay to ensure container is rendered
    if (!loading && videoData) {
      setTimeout(initializeCrop, 100);
    }
  }, [loading, videoData]);

  const getAspectRatio = (ratio: string) => {
    switch (ratio) {
      case "1:1":
        return 1;
      case "9:16":
        return 9 / 16;
      case "16:9":
        return 16 / 9;
      default:
        return null;
    }
  };

  const getSvgFilter = (isSelected: boolean) => {
    if (isSelected) {
      return "brightness(0) saturate(100%) invert(27%) sepia(91%) saturate(636%) hue-rotate(232deg) brightness(92%) contrast(95%)";
    } else {
      return "brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%)";
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  const handleTimelineClick = (e: React.MouseEvent) => {
    if (!videoData) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newPosition = Math.max(0, Math.min(1, clickX / rect.width));
    const newTime = newPosition * videoData.duration;

    setTimelinePosition(newPosition);
    setCurrentTime(newTime);

    if (videoRef.current) {
      videoRef.current.currentTime = newTime;
    }
  };

  // Update timeline position based on current time
  useEffect(() => {
    if (videoData && videoData.duration > 0) {
      setTimelinePosition(currentTime / videoData.duration);
    }
  }, [currentTime, videoData]);

  // Video time update handler
  const handleTimeUpdate = () => {
    if (videoRef.current && videoData) {
      const time = videoRef.current.currentTime;
      setCurrentTime(time);
    }
  };

  // Play/Pause functionality
  const togglePlayPause = () => {
    if (!videoRef.current) return;

    if (isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleRatioSelect = (ratio: string) => {
    setSelectedRatio(ratio);
    // Auto-adjust crop area based on ratio
    const containerRect = containerRef.current?.getBoundingClientRect();
    if (!containerRect) return;

    setCropArea((prev) => {
      let newArea = { ...prev };

      // Get image container bounds
      const imageContainer =
        containerRect.width > containerRect.height
          ? {
              width: Math.min(containerRect.width, 320),
              height: containerRect.height,
            }
          : { width: containerRect.width, height: containerRect.height };

      const imageX = (containerRect.width - imageContainer.width) / 2;
      const imageY = (containerRect.height - imageContainer.height) / 2;

      // Calculate max dimensions within image bounds
      const maxWidth = imageContainer.width * 0.8;
      const maxHeight = imageContainer.height * 0.8;

      if (ratio === "1:1") {
        const size = Math.min(maxWidth, maxHeight);
        newArea.width = size;
        newArea.height = size;
      } else if (ratio === "9:16") {
        newArea.width = Math.min(maxWidth, maxHeight * (9 / 16));
        newArea.height = newArea.width * (16 / 9);
      } else if (ratio === "16:9") {
        newArea.height = Math.min(maxHeight, maxWidth * (9 / 16));
        newArea.width = newArea.height * (16 / 9);
      }

      // Center the crop area within image bounds
      newArea.x = imageX + (imageContainer.width - newArea.width) / 2;
      newArea.y = imageY + (imageContainer.height - newArea.height) / 2;

      return newArea;
    });
  };

  // Save crop data
  const handleSave = async () => {
    if (!videoData) return;

    const cropData = {
      videoId: videoData.id,
      cropArea: {
        x: cropArea.x,
        y: cropArea.y,
        width: cropArea.width,
        height: cropArea.height,
      },
      aspectRatio: selectedRatio,
      timeline: {
        currentTime: currentTime,
        duration: videoData.duration,
        position: timelinePosition,
      },
      timestamp: new Date().toISOString(),
    };

    try {
      setIsSaving(true);
    } catch (error) {
      console.error("Save error:", error);
      setIsSaving(false);
      alert("Failed to save crop. Please try again.");
    }
  };

  // Handle save completion
  const handleSaveComplete = () => {
    setIsSaving(false);
  };

  // Handle save cancellation
  const handleSaveCancel = () => {
    setIsSaving(false);
  };

  // Loading state
  if (loading) {
    return (
      <main className="min-h-screen bg-neutral-900 text-white flex flex-col items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        <p className="mt-4 text-white/70">Loading video...</p>
      </main>
    );
  }

  // Error state
  if (error || !videoData) {
    return (
      <main className="min-h-screen bg-neutral-900 text-white flex flex-col items-center justify-center">
        <p className="text-red-400">{error || "Video not found"}</p>
        <Link href="/mobile" className="mt-4 text-blue-400 underline">
          Go back
        </Link>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-neutral-900 text-white flex flex-col">
      {/* Header */}
      <header className="sticky top-0 z-40 bg-gradient-to-b from-primary-2 to-primary">
        <div className="px-4 py-3 flex items-center gap-6">
          <Link
            href="/mobile"
            className="inline-flex h-8 w-8 items-center justify-center rounded-full hover:bg-white/10"
            aria-label="Back"
          >
            <Image
              src="/assets/svg/back.svg"
              alt="Back"
              width={19}
              height={19}
            />
          </Link>
          <h1 className="text-lg font-medium">Crop </h1>
        </div>
      </header>

      {/* Video Player Area with Crop Overlay */}
      <section
        className="flex-1 bg-black relative overflow-hidden"
        ref={containerRef}
      >
        <div className="absolute inset-0 flex items-center justify-center">
          <div
            className="relative w-full h-full max-w-xs mx-auto"
            style={{ touchAction: "none" }}
          >
            {/* Video Display */}
            <div className="absolute inset-0">
              {/* Video thumbnail - dynamic based on slug data */}
              <div
                className="w-full h-full bg-cover bg-center"
                style={{ backgroundImage: `url('${videoData.thumbnail}')` }}
              >
                <div className="absolute inset-0 bg-black/20" />

                {/* Video info overlay */}
                <div className="absolute top-4 right-4 bg-black/80 text-white text-xs px-2 py-1 rounded backdrop-blur-sm">
                  {videoData.size} • {formatTime(videoData.duration)}
                </div>

                {/* Hidden video element for functionality */}
                {videoData.url && (
                  <video
                    ref={videoRef}
                    className="absolute inset-0 w-full h-full object-cover opacity-0 pointer-events-none"
                    onTimeUpdate={handleTimeUpdate}
                    onPlay={() => setIsPlaying(true)}
                    onPause={() => setIsPlaying(false)}
                  >
                    <source src={videoData.url} type="video/mp4" />
                  </video>
                )}
              </div>
            </div>

            {/* Dark overlay outside crop area with cutout */}
            <div
              className="absolute inset-0 "
              style={{
                clipPath: `polygon(
                  0% 0%, 
                  0% 100%, 
                  ${cropArea.x}px 100%, 
                  ${cropArea.x}px ${cropArea.y}px, 
                  ${cropArea.x + cropArea.width}px ${cropArea.y}px, 
                  ${cropArea.x + cropArea.width}px ${
                  cropArea.y + cropArea.height
                }px, 
                  ${cropArea.x}px ${cropArea.y + cropArea.height}px, 
                  ${cropArea.x}px 100%, 
                  100% 100%, 
                  100% 0%
                )`,
              }}
            />

            {/* Draggable Crop Rectangle */}
            <div
              ref={cropRef}
              className={`absolute select-none transition-all duration-200 cursor-move ${
                isActive ? "shadow-lg shadow-white/20" : ""
              }`}
              style={{
                left: cropArea.x,
                top: cropArea.y,
                width: cropArea.width,
                height: cropArea.height,
                border: "2px solid white",
                boxShadow: isActive
                  ? "0 0 0 1px rgba(255,255,255,0.3), inset 0 0 0 1px rgba(255,255,255,0.2)"
                  : "none",
                touchAction: "none",
                userSelect: "none",
                zIndex: 10,
              }}
              onMouseDown={handleMouseDown}
              onTouchStart={handleMouseDown}
              onContextMenu={(e) => e.preventDefault()}
            >
              {/* Grid Lines */}
              <div className="absolute inset-0 pointer-events-none">
                {/* Rule of thirds lines */}
                <div className="absolute left-1/3 top-0 w-px h-full bg-white/40" />
                <div className="absolute left-2/3 top-0 w-px h-full bg-white/40" />
                <div className="absolute top-1/3 left-0 w-full h-px bg-white/40" />
                <div className="absolute top-2/3 left-0 w-full h-px bg-white/40" />
                {/* Center lines */}
                <div className="absolute left-1/2 top-0 w-px h-full bg-white/20" />
                <div className="absolute top-1/2 left-0 w-full h-px bg-white/20" />
              </div>

              {/* Corner Resize Handles */}
              <div
                className="absolute -top-1 -left-1 w-4 h-4 cursor-nw-resize hover:scale-125 transition-all duration-200 z-10"
                onMouseDown={(e) => handleResizeMouseDown(e, "top-left")}
                onTouchStart={(e) => handleResizeMouseDown(e, "top-left")}
              >
                <div className="w-full h-full bg-white rounded-sm shadow-lg border border-gray-300 flex items-center justify-center">
                  <Image
                    src="/images/svg/bogoc.svg"
                    alt="crop"
                    width={10}
                    height={10}
                    className="opacity-80"
                  />
                </div>
              </div>
              <div
                className="absolute -top-1 -right-1 w-4 h-4 cursor-ne-resize hover:scale-125 transition-all duration-200 z-10"
                onMouseDown={(e) => handleResizeMouseDown(e, "top-right")}
                onTouchStart={(e) => handleResizeMouseDown(e, "top-right")}
              >
                <div className="w-full h-full bg-white rounded-sm shadow-lg border border-gray-300 flex items-center justify-center">
                  <Image
                    src="/images/svg/bogoc.svg"
                    alt="crop"
                    width={10}
                    height={10}
                    className="opacity-80 rotate-90"
                  />
                </div>
              </div>
              <div
                className="absolute -bottom-1 -left-1 w-4 h-4 cursor-sw-resize hover:scale-125 transition-all duration-200 z-10"
                onMouseDown={(e) => handleResizeMouseDown(e, "bottom-left")}
                onTouchStart={(e) => handleResizeMouseDown(e, "bottom-left")}
              >
                <div className="w-full h-full bg-white rounded-sm shadow-lg border border-gray-300 flex items-center justify-center">
                  <Image
                    src="/images/svg/bogoc.svg"
                    alt="crop"
                    width={10}
                    height={10}
                    className="opacity-80 -rotate-90"
                  />
                </div>
              </div>
              <div
                className="absolute -bottom-1 -right-1 w-4 h-4 cursor-se-resize hover:scale-125 transition-all duration-200 z-10"
                onMouseDown={(e) => handleResizeMouseDown(e, "bottom-right")}
                onTouchStart={(e) => handleResizeMouseDown(e, "bottom-right")}
              >
                <div className="w-full h-full bg-white rounded-sm shadow-lg border border-gray-300 flex items-center justify-center">
                  <Image
                    src="/images/svg/bogoc.svg"
                    alt="crop"
                    width={10}
                    height={10}
                    className="opacity-80 rotate-180"
                  />
                </div>
              </div>

              {/* Edge Handles */}
              <div
                className="absolute top-0 left-1/2 w-6 h-2 -mt-1 -ml-3 cursor-n-resize hover:bg-white/20 transition-colors rounded-sm"
                onMouseDown={(e) => handleResizeMouseDown(e, "top")}
                onTouchStart={(e) => handleResizeMouseDown(e, "top")}
              />
              <div
                className="absolute bottom-0 left-1/2 w-6 h-2 -mb-1 -ml-3 cursor-s-resize hover:bg-white/20 transition-colors rounded-sm"
                onMouseDown={(e) => handleResizeMouseDown(e, "bottom")}
                onTouchStart={(e) => handleResizeMouseDown(e, "bottom")}
              />
              <div
                className="absolute left-0 top-1/2 w-2 h-6 -ml-1 -mt-3 cursor-w-resize hover:bg-white/20 transition-colors rounded-sm"
                onMouseDown={(e) => handleResizeMouseDown(e, "left")}
                onTouchStart={(e) => handleResizeMouseDown(e, "left")}
              />
              <div
                className="absolute right-0 top-1/2 w-2 h-6 -mr-1 -mt-3 cursor-e-resize hover:bg-white/20 transition-colors rounded-sm"
                onMouseDown={(e) => handleResizeMouseDown(e, "right")}
                onTouchStart={(e) => handleResizeMouseDown(e, "right")}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Bottom Controls Panel */}
      <section className=" p-4 space-y-4 bg-black">
        {/* Timeline */}
        <div className="space-y-2">
          <div className="relative">
            <div
              className="w-full h-1 rounded-full cursor-pointer"
              onClick={handleTimelineClick}
            >
              <div
                className="h-full bg-white rounded-full transition-all duration-200"
                style={{ width: `${timelinePosition * 100}%` }}
              />
            </div>
            <div
              className="absolute top-1/2 w-3 h-3 bg-white rounded-full -translate-y-1/2 transition-all duration-200"
              style={{ left: `${timelinePosition * 100}%`, marginLeft: "-6px" }}
            />
          </div>

          {/* Time Indicators */}
          <div className="flex justify-between text-lg text-white/80">
            <span className="text-lg">{formatTime(currentTime)}</span>
            <span className="text-lg">{formatTime(videoData.duration)}</span>
          </div>
        </div>

        {/* Crop Ratio Buttons */}
        <div className="space-y-3">
          <div className="grid grid-cols-4 gap-2">
            <button
              onClick={() => handleRatioSelect("custom")}
              className={`py-2.5 px-3.5 rounded-md text-sm font-medium transition-colors ${
                selectedRatio === "custom"
                  ? "bg-white text-[#4020A1]"
                  : "bg-neutral-700 text-white hover:bg-neutral-600"
              }`}
            >
              Tùy chỉnh
            </button>

            <button
              onClick={() => handleRatioSelect("1:1")}
              className={`py-2.5 px-3.5 rounded-md font-medium transition-colors flex justify-center items-center gap-2 ${
                selectedRatio === "1:1"
                  ? "bg-white text-primary"
                  : "bg-neutral-700 text-white hover:bg-neutral-600"
              }`}
            >
              <Image
                src="/assets/svg/ig.svg"
                alt="1:1"
                width={19}
                height={19}
                style={{ filter: getSvgFilter(selectedRatio === "1:1") }}
              />
              1:1
            </button>

            <button
              onClick={() => handleRatioSelect("9:16")}
              className={`py-2.5 px-3.5 rounded-md font-medium transition-colors flex justify-center items-center gap-2 ${
                selectedRatio === "9:16"
                  ? "bg-white text-primary"
                  : "bg-neutral-700 text-white hover:bg-neutral-600"
              }`}
            >
              <Image
                src="/assets/svg/tiktok.svg"
                alt="9:16"
                width={19}
                height={19}
                style={{ filter: getSvgFilter(selectedRatio === "9:16") }}
              />
              9:16
            </button>

            <button
              onClick={() => handleRatioSelect("16:9")}
              className={`py-2 px-2 rounded-md font-medium transition-colors flex justify-center items-center gap-2 ${
                selectedRatio === "16:9"
                  ? "bg-white text-primary"
                  : "bg-neutral-700 text-white hover:bg-neutral-600"
              }`}
            >
              <Image
                src="/assets/svg/youtube.svg"
                alt="16:9"
                width={19}
                height={19}
                style={{ filter: getSvgFilter(selectedRatio === "16:9") }}
              />
              16:9
            </button>
          </div>
        </div>
      </section>

      {/* Save Button */}
      <footer className="sticky bottom-0">
        <button
          className="w-full bg-primary hover:bg-primary-2 text-white text-center py-4.5 font-medium text-lg transition-colors"
          onClick={handleSave}
        >
          SAVE
        </button>
      </footer>

      {/* Saving Overlay */}
      {isSaving && (
        <SavingOverlay
          videoThumbnail={videoData.thumbnail}
          onCancel={handleSaveCancel}
          onComplete={handleSaveComplete}
        />
      )}
    </main>
  );
}
