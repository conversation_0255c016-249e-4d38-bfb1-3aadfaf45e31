"use client";

import Link from "next/link";
import Image from "next/image";
import React from "react";

const footerLinks = [
  { label: "About Us", href: "/about" },
  { label: "Privacy", href: "/privacy" },
  { label: "Terms", href: "/terms" },
  { label: "Security and Compliance", href: "/security" },
  { label: "Contact", href: "/contact" },
  { label: "Status", href: "/status" },
];

const FooterPC: React.FC = () => {
  return (
    <footer className="bg-gray-800 text-gray-300 text-sm">
      <div className="w-content mx-auto px-6 py-6 flex items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center gap-2">
          <span className="relative h-6 w-6">
            <Image src="/assets/svg/fast-logo.svg" alt="FASTconvert" fill />
          </span>
          <span className="font-bold text-[35px] text-white italic">
            <span className="text-primary">ROCKET </span>convert
          </span>
        </Link>

        {/* Menu */}
        <nav className="hidden md:flex gap-6 text-gray-300">
          {footerLinks.map((item) => (
            <Link
              key={item.label}
              href={item.href}
              className="hover:text-white transition text-18 font-light"
            >
              {item.label}
            </Link>
          ))}
        </nav>

        {/* Language selector */}
        <div className="relative group cursor-pointer text-18">
          <button
            className="flex items-center gap-1 hover:text-white"
            aria-haspopup="menu"
            aria-expanded="false"
          >
            English
            <svg
              className="h-3 w-3"
              viewBox="0 0 16 16"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.6"
            >
              <path d="M4 6l4 4 4-4" />
            </svg>
          </button>

          {/* Mở LÊN + hiệu ứng slide-up/fade */}
          <div
            role="menu"
            className="
              absolute right-0 bottom-full mb-2 min-w-[160px]
              rounded-md border border-gray-700 bg-gray-900 shadow-lg
              opacity-0 translate-y-2 scale-95 origin-bottom-right
              transition-all duration-200 ease-out
              pointer-events-none
              group-hover:opacity-100 group-hover:translate-y-0 group-hover:scale-100
              group-hover:pointer-events-auto
              focus-within:opacity-100 focus-within:translate-y-0 focus-within:scale-100
            "
          >
            <button
              role="menuitem"
              className="block px-4 py-2 w-full text-left hover:bg-gray-700"
            >
              English
            </button>
            <button
              role="menuitem"
              className="block px-4 py-2 w-full text-left hover:bg-gray-700"
            >
              Vietnamese
            </button>
          </div>
        </div>
      </div>

      {/* Bottom bar */}
      <div className="bg-gray-900 text-center py-3 text-base text-gray-400">
        Fastconvertforvideo © {new Date().getFullYear()}
      </div>
    </footer>
  );
};

export default FooterPC;
