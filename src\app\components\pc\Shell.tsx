"use client";

import { useState } from "react";
import { usePathname } from "next/navigation";
import HeaderPc from "./header/Header";
import FooterPC from "./header/Footer";

const TOOLS = ["trim", "convert", "crop", "rotation"];

export default function Shell({ children }: { children: React.ReactNode }) {
  const [open, setOpen] = useState(false);
  const pathname = usePathname();

  const useToolFromPath = (pathname: string) => {
    const segs = pathname.split("/").filter(Boolean); // ["mobile","trim","1"]
    return segs[1] ?? ""; // "trim"
  };

  const showHeader = !TOOLS.includes(useToolFromPath(pathname));

  return (
    <div className="min-h-screen bg-white text-black flex flex-col">
      {/* Content wrapper
          - Khi sidebar mở, nội dung dịch padding-left 72 (desktop) mượt
       */}
      {showHeader && <HeaderPc />}
      <main className="flex-1">{children}</main>
      <FooterPC />
    </div>
  );
}
