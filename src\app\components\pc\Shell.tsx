"use client";
// import { usePathname } from "next/navigation";
import HeaderPc from "./header/Header";
import FooterPC from "./footer/Footer";

// const TOOLS = ["trim", "convert", "crop", "rotation"];

export default function PCShell({ children }: { children: React.ReactNode }) {
  // const pathname = usePathname();

  // const useToolFromPath = (pathname: string) => {
  //   const segs = pathname.split("/").filter(Boolean); // ["mobile","trim","1"]
  //   return segs[1] ?? ""; // "trim"
  // };

  // const showHeader = !TOOLS.includes(useToolFromPath(pathname));

  return (
    <div className="min-h-dvh flex flex-col bg-white text-black">
      {/* Content wrapper
          - Khi sidebar mở, nội dung dịch padding-left 72 (desktop) mượt
       */}
      <HeaderPc />
      <main className="flex-1">{children}</main>
      <FooterPC />
    </div>
  );
}
