"use client";
import React, { useState } from "react";

type Tool = "convert" | "trim" | "crop" | "rotation";

type Props = {
  open: boolean; // hiển thị khi true
  selectedCount: number; // số item đã chọn
  totalSizeLabel: string; // tổng dung lượng, vd "32MB"
  onClear?: () => void; // clear selection
  onNext?: () => void; // đi tiếp
  className?: string;
};

const tools: { key: Tool; label: string; icon: React.ReactNode }[] = [
  {
    key: "convert",
    label: "Convert",
    icon: (
      <svg
        viewBox="0 0 24 24"
        width="18"
        height="18"
        fill="currentColor"
        aria-hidden
      >
        <path d="M7 7h10v2H7zM7 11h10v2H7zM7 15h10v2H7z" />
      </svg>
    ),
  },
  {
    key: "trim",
    label: "Trim",
    icon: (
      <svg
        viewBox="0 0 24 24"
        width="18"
        height="18"
        fill="currentColor"
        aria-hidden
      >
        <path d="M7 3h2v18H7zM15 3h2v18h-2zM5 7h14v2H5zM5 15h14v2H5z" />
      </svg>
    ),
  },
  {
    key: "crop",
    label: "Crop",
    icon: (
      <svg
        viewBox="0 0 24 24"
        width="18"
        height="18"
        fill="currentColor"
        aria-hidden
      >
        <path d="M6 2v4H2v2h4v8a2 2 0 0 0 2 2h8v4h2v-4h4v-2h-4V6a2 2 0 0 0-2-2H8V0H6v2zm4 4h6v10H8V6h2z" />
      </svg>
    ),
  },
  {
    key: "rotation",
    label: "Rotation",
    icon: (
      <svg
        viewBox="0 0 24 24"
        width="18"
        height="18"
        fill="currentColor"
        aria-hidden
      >
        <path d="M12 2v4l3-3-3-1zM4 12a8 8 0 1 0 8-8h-1v2h1a6 6 0 1 1-6 6H4z" />
      </svg>
    ),
  },
];

export default function FooterActionBar({
  open,
  selectedCount,
  totalSizeLabel,
  onClear,
  onNext,
  className,
}: Props) {
  const [activeTool, setActiveTool] = useState<Tool>("trim");

  const handleSelectTool = (key: Tool) => {
    setActiveTool(key);
  };

  return (
    <div
      className={`fixed left-0 right-0 bottom-0 z-40 transition-transform duration-300
      ${open ? "translate-y-0" : "translate-y-full"}
      ${className || ""}`}
      aria-hidden={!open}
    >
      {/* Hộp trên: tool buttons */}
      <div className="mx-auto max-w-5xl rounded-t-[4px] bg-gradient-to-b from-primary-2 to-primary backdrop-blur text-white shadow-xl border border-primary">
        <div className="flex flex-wrap gap-3 p-3">
          {tools.map((t) => {
            const active = t.key === activeTool;
            return (
              <button
                key={t.key}
                onClick={() => handleSelectTool(t.key)}
                className={`inline-flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium
                  ring-1 ring-white/20
                  ${
                    active
                      ? "bg-white text-primary"
                      : "bg-primary hover:bg-primary-2"
                  }
                `}
                aria-pressed={active}
              >
                <span className={`${active ? "text-primary" : "text-white"}`}>
                  {t.icon}
                </span>
                {t.label}
              </button>
            );
          })}
        </div>

        {/* Đường chia */}
        <div className="h-px w-full bg-white/15" />

        {/* Hộp dưới: selected + next */}
        <div className="flex items-center justify-between p-3">
          <button
            onClick={onClear}
            className="inline-flex items-center gap-2 text-sm font-medium text-white/90 hover:text-white"
          >
            <svg
              viewBox="0 0 24 24"
              width="18"
              height="18"
              fill="currentColor"
              aria-hidden
            >
              <path d="M18.3 5.71L12 12.01l-6.29-6.3-1.42 1.42 6.3 6.29-6.3 6.29 1.42 1.42 6.29-6.3 6.29 6.3 1.42-1.42-6.3-6.29 6.3-6.29z" />
            </svg>
            <span>
              {selectedCount} selected ({totalSizeLabel})
            </span>
          </button>

          <button
            onClick={onNext}
            className="inline-flex items-center gap-2 rounded-full bg-white text-primary px-4 py-2 font-semibold shadow hover:opacity-95"
          >
            Next
            <svg
              viewBox="0 0 24 24"
              width="18"
              height="18"
              fill="currentColor"
              aria-hidden
            >
              <path d="M10 17l5-5-5-5v10z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}
