"use client";

import Link from "next/link";
import {
  HomeOutlined,
  VideoCameraOutlined,
  FolderOpenOutlined,
  CompressOutlined,
  SettingOutlined,
  CloseOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { useEffect } from "react";

type Props = { open: boolean; onClose: () => void };

const nav = [
  { href: "/", label: "Dashboard", icon: <HomeOutlined /> },
  // { href: "/admin", label: "Admin", icon: <UserOutlined /> },
  { href: "/videos", label: "All Videos", icon: <VideoCameraOutlined /> },
  { href: "/folders", label: "Folders", icon: <FolderOpenOutlined /> },
  { href: "/compressed", label: "Compressed", icon: <CompressOutlined /> },
  { href: "/settings", label: "Settings", icon: <SettingOutlined /> },
];

export default function SideBar({ open, onClose }: Props) {
  useEffect(() => {
    const onKey = (e: KeyboardEvent) => e.key === "Escape" && onClose();
    window.addEventListener("keydown", onKey);
    return () => window.removeEventListener("keydown", onKey);
  }, [onClose]);

  return (
    <>
      {/* Overlay chỉ hiện mobile */}
      <div
        className={[
          "fixed inset-0 z-40 bg-black/40 backdrop-blur-sm transition-opacity duration-300 ease-out lg:hidden",
          open
            ? "opacity-100 pointer-events-auto"
            : "opacity-0 pointer-events-none",
        ].join(" ")}
        onClick={onClose}
      />

      {/* Panel */}
      <aside
        className={[
          "fixed top-0 left-0 z-50 h-screen w-72",
          "bg-gradient-to-b from-[#6A11CB] to-[#8E2DE2] text-white shadow-xl",
          "transform will-change-transform transition-transform duration-400 ease-[cubic-bezier(0.22,1,0.36,1)]",
          open ? "translate-x-0" : "-translate-x-full",
        ].join(" ")}
        aria-hidden={!open}
      >
        {/* Brand + Close */}
        <div className="h-16 flex items-center justify-between px-4 border-b border-white/10">
          <span className="text-base font-semibold truncate">
            Fast Converter
          </span>
          <button
            onClick={onClose}
            aria-label="Close sidebar"
            className="grid place-items-center size-9 rounded-xl hover:bg-white/10 transition"
          >
            <CloseOutlined className="text-lg" />
          </button>
        </div>

        {/* Nav */}
        <nav className="p-3 space-y-1">
          {nav.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className="flex items-center gap-3 px-3 h-11 rounded-xl hover:bg-white/10 active:bg-white/15 transition text-sm font-medium"
              onClick={onClose}
            >
              <span className="text-lg">{item.icon}</span>
              <span className="truncate">{item.label}</span>
            </Link>
          ))}
        </nav>

        {/* Bottom card */}
        <div className="absolute bottom-3 left-3 right-3">
          <div className="rounded-xl bg-white/10 p-3 text-xs leading-5">
            <div className="font-semibold mb-1">Storage</div>
            <div className="h-2 w-full bg-white/20 rounded overflow-hidden">
              <div className="h-full w-2/3 bg-white/80" />
            </div>
            <div className="mt-1 opacity-80">6.7GB / 10GB</div>
          </div>
        </div>
      </aside>
    </>
  );
}
