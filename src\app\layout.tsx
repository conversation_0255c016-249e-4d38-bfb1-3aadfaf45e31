import type { Metada<PERSON> } from "next";
import { headers } from 'next/headers';
import { AntdRegistry } from "@ant-design/nextjs-registry";
import { <PERSON><PERSON> } from "next/font/google";
import "@/globals.css";
import { ViewModeProvider } from "@/contexts/ViewModeContext";
import { TabProvider } from "@/contexts/TabContext";
import { FolderProvider } from "@/contexts/FolderContext";
import { CompressedViewProvider } from "@/contexts/CompressedViewContext";
import PcShell from "@/components/pc/Shell";
import MobileShell from "@/components/mobile/Shell";

const roboto = Roboto({
  subsets: ["latin"], // chọn subset cần
  weight: ["400", "500", "600", "700", "800", "900"], // chọn độ đậm
  variable: "--font-roboto", // CSS variable
});

export const metadata: Metadata = {
  title: "Rocket Converter",
  description: "Convert video file fast and convenient",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const header = await headers();
  const ua = header.get("user-agent") || "";
  const isMobile =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua);
  
  return (
    <html lang="en">
      <body className={`${roboto.variable} antialiased`}>
        <AntdRegistry>
          <ViewModeProvider>
            <TabProvider>
              <FolderProvider>
                <CompressedViewProvider>
                  {isMobile
                  ? <MobileShell>{children}</MobileShell>
                  :<PcShell>{children}</PcShell>}
                </CompressedViewProvider>
              </FolderProvider>
            </TabProvider>
          </ViewModeProvider>
        </AntdRegistry>
      </body>
    </html>
  );
}
