import type { Metadata } from "next";
import { headers } from 'next/headers';
import { AntdRegistry } from "@ant-design/nextjs-registry";
import { <PERSON><PERSON> } from "next/font/google";
import "@/globals.css";
import { ViewModeProvider } from "@/contexts/ViewModeContext";
import { TabProvider } from "@/contexts/TabContext";
import { FolderProvider } from "@/contexts/FolderContext";
import { CompressedViewProvider } from "@/contexts/CompressedViewContext";
// import { usePathname } from "next/navigation";
// import PCLayout from "@/pc/layout";
// import MobileLayout from "@/mobile/layout";
import Shell from "@/components/pc/Shell";

const roboto = Roboto({
  subsets: ["latin"], // chọn subset cần
  weight: ["400", "500", "600", "700", "800", "900"], // chọn độ đậm
  variable: "--font-roboto", // CSS variable
});

export const metadata: Metadata = {
  title: "Rocket Converter",
  description: "Convert video file fast and convenient",
};

// const TOOLS = ["trim", "convert", "crop", "rotation"];

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ team: string }>;
}>) {
  const header = await headers();
  const pathname = header.get('x-pathname');
  const referer = header.get('referer');
  const host = header.get('host');
  const isMobile = header.get('x-is-mobile');
  console.log("-------------\n\nheaders: ", {referer, host, isMobile, pathname}, header, "\n\n");

  return (
    <html lang="en">
      <body className={`${roboto.variable} antialiased`}>
        <AntdRegistry>
          <ViewModeProvider>
            <TabProvider>
              <FolderProvider>
                <CompressedViewProvider>
                  {/* <>{children}</> */}
                  {isMobile
                  ? <>{children}</>
                  :<Shell>{children}</Shell>}
                </CompressedViewProvider>
              </FolderProvider>
            </TabProvider>
          </ViewModeProvider>
        </AntdRegistry>
      </body>
    </html>
  );
}
