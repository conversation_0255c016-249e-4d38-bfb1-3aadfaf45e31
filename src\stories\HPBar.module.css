/* CSS Module chỉ cho HP bar effects và animations */

/* Animated bar container */
.animatedBar {
  animation: barFloat 4s ease-in-out infinite;
}

@keyframes barFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-1px);
  }
}

/* Animated numbers */
.animatedNumber {
  animation: numberPulse 2s ease-in-out infinite;
}

@keyframes numberPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* HP Progress bar base */
.hpProgress {
  position: relative;
  overflow: hidden;
}

/* HP Fill với gradient và animation */
.hpFill {
  background: linear-gradient(
    90deg,
    currentColor 0%,
    rgba(255, 255, 255, 0.2) 50%,
    currentColor 100%
  );
  background-size: 200% 100%;
  animation: fillShimmer 2s ease-in-out infinite;
}

@keyframes fillShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Low HP effects */
.lowHP {
  animation: lowHPWarning 2s ease-in-out infinite;
}

@keyframes lowHPWarning {
  0%,
  100% {
    filter: brightness(1);
  }
  50% {
    filter: brightness(1.1);
  }
}

.lowHP .hpFill {
  background: linear-gradient(90deg, #ef4444 0%, #fca5a5 50%, #ef4444 100%);
  animation: lowHPPulse 1.5s ease-in-out infinite;
}

@keyframes lowHPPulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Critical HP effects */
.criticalHP {
  animation: criticalShake 0.5s ease-in-out infinite;
}

@keyframes criticalShake {
  0%,
  100% {
    transform: translateX(0px);
  }
  25% {
    transform: translateX(-1px);
  }
  75% {
    transform: translateX(1px);
  }
}

.criticalHP .hpFill {
  background: linear-gradient(
    90deg,
    #dc2626 0%,
    #f87171 25%,
    #dc2626 50%,
    #f87171 75%,
    #dc2626 100%
  );
  background-size: 400% 100%;
  animation: criticalFlash 0.8s ease-in-out infinite;
}

@keyframes criticalFlash {
  0% {
    background-position: 0% 0;
    opacity: 1;
  }
  50% {
    background-position: 100% 0;
    opacity: 0.7;
  }
  100% {
    background-position: 200% 0;
    opacity: 1;
  }
}

/* Critical overlay */
.criticalOverlay {
  background: rgba(239, 68, 68, 0.2);
  animation: criticalOverlayPulse 1s ease-in-out infinite alternate;
  pointer-events: none;
}

@keyframes criticalOverlayPulse {
  0% {
    opacity: 0.2;
  }
  100% {
    opacity: 0.5;
  }
}

/* Force critical state */
.forceCritical .hpFill {
  background: linear-gradient(90deg, #dc2626 0%, #f87171 50%, #dc2626 100%);
  animation: forceCriticalPulse 1s ease-in-out infinite;
}

@keyframes forceCriticalPulse {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
  }
  50% {
    box-shadow: 0 0 15px rgba(239, 68, 68, 0.8);
  }
}

/* Change indicator for damage/heal animations */
.changeIndicator {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  transform: translateX(-100%);
  animation: changeFlash 0.6s ease-out;
  pointer-events: none;
  opacity: 0;
}

@keyframes changeFlash {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Hover effects */
.animatedBar:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

.animatedBar:hover .hpFill {
  animation-duration: 1s;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .animatedBar {
    animation: none;
  }

  .criticalHP {
    animation: none;
  }

  .lowHP {
    animation: none;
  }
}
