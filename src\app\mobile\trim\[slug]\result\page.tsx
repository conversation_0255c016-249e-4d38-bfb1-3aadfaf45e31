"use client";
import VideoInfoCard from "@/components/mobile/video/VideoInfoCard";
import SaveFolderPopup from "@/components/Shared/popup/SaveFolderPopup";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useState } from "react";
// import { useParams } from "next/navigation";

export default function TrimResultPage() {
  const { slug } = useParams();
  const [openSave, setOpenSave] = useState(false);

  const toggleSave = () => {
    setOpenSave(!openSave);
  };

  return (
    <main className="min-h-screen bg-neutral-900 text-white flex flex-col">
      {/* Header */}
      <header className="sticky top-0 z-40 bg-gradient-to-b from-primary-2 to-primary">
        <div className="px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Link
              href={`/mobile/trim/${slug}`}
              className="inline-flex h-8 w-8 items-center justify-center rounded-full hover:bg-white/10"
              aria-label="Back"
            >
              <Image
                src="/assets/svg/back.svg"
                alt="Back"
                width={19}
                height={19}
              />
            </Link>
            <h1 className="text-base font-semibold">Result</h1>
          </div>
          <h1 className="text-base font-semibold">15MB</h1>
        </div>
      </header>

      {/* Player area */}
      <section className="flex-1 bg-black relative">
        <div className="px-2 pt-2">
          <VideoInfoCard
            thumbnail="/assets/images/Video1.png"
            title="Hội cựu sinh viên ĐH QG"
            original={{ size: "32MB", resolution: "1280x720" }}
            compressed={{ size: "17MB", duration: "00:00:05" }}
          />
        </div>
      </section>

      {/* Bottom editor panel */}
      <section className="bg-neutral-900">
        {/* Title row */}
        <div className="px-4 py-2 text-center text-sm">
          <span className="inline-block truncate max-w-[92%]">
            Hội cựu sinh viên ĐH QG
          </span>
        </div>

        {/* Timeline strip (mock) */}
        <div className="px-4 pb-2">
          <div className="relative h-16 rounded-md bg-neutral-800 overflow-hidden">
            <div className="absolute inset-0 grid grid-cols-8">
              {Array.from({ length: 8 }).map((_, i) => (
                <div
                  key={i}
                  className="bg-[url('/assets/images/Video1.png')] bg-cover bg-center opacity-70"
                />
              ))}
            </div>
            <div className="absolute inset-y-0 left-12 right-12 ring-2 ring-white/90 rounded-sm" />
            <div className="absolute inset-y-0 left-12 w-[6px] bg-white rounded-sm shadow" />
            <div className="absolute inset-y-0 right-12 w-[6px] bg-white rounded-sm shadow" />
          </div>

          <div className="mt-2 flex justify-between text-xs text-white/80">
            <span>00:00:01</span>
            <span>00:00:15</span>
          </div>
        </div>
      </section>

      {/* Footer actions */}
      <footer className="sticky bottom-0">
        <div className="flex gap-1">
          <button
            className="flex-1 !bg-primary hover:!bg-primary-2 text-white text-center !text-[22px] !py-4 font-semibold tracking-wide flex justify-center gap-4"
            onClick={toggleSave}
          >
            <Image
              src="/assets/svg/downloadIcon.svg"
              alt="Save"
              width={18.75}
              height={18.75}
            />
            Save
          </button>
        </div>
      </footer>
      <SaveFolderPopup
        open={openSave}
        content={"/storage/emulated/Move/videoconvert"}
        onClose={toggleSave}
      />
    </main>
  );
}
