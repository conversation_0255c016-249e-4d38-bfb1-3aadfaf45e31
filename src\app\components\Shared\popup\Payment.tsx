"use client";

import React, { useState } from "react";
import { Card, Input, Select, Button, Form, Modal } from "antd";
import {
  CreditCardOutlined,
  LockOutlined,
  GlobalOutlined,
} from "@ant-design/icons";
import countryOptions from "@lib/locations.json";

export type PaymentModalProps = {
  open: boolean;
  onClose: () => void;
  title?: React.ReactNode | null;
  /** Body content (put your own buttons here) */
  children?: React.ReactNode;
  /** Misc */
  maskClosable?: boolean;
  closable?: boolean; // default false
  width?: number | string; // default: min(92vw, 420px)
  className?: string;
};

export function CardIcons () {
  return (
    <div
      className="flex flex-row gap-1"
      aria-describedby="cardBrandIconsDesc"
    >
      {/* <p id="cardBrandIconsDesc" className="u-visually-hidden">
        <PERSON><PERSON><PERSON> thẻ được hỗ trợ bao gồm <PERSON>, Mastercard, American Express và JCB.
      </p> */}
      <div className="p-CardBrandIcons-item" data-testid="visa">
        <svg
          viewBox="0 0 24 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          role="presentation"
          focusable="false"
          className="w-8"
        >
          <g>
            <rect
              stroke="#DDD"
              fill="#FFF"
              x=".25"
              y=".25"
              width="23.5"
              height="15.5"
              rx="2"
            ></rect>
            <path
              d="M2.788 5.914A7.201 7.201 0 0 0 1 5.237l.028-.125h2.737c.371.013.672.125.77.519l.595 2.836.182.854 1.666-4.21h1.799l-2.674 6.167H4.304L2.788 5.914Zm7.312 5.37H8.399l1.064-6.172h1.7L10.1 11.284Zm6.167-6.021-.232 1.333-.153-.066a3.054 3.054 0 0 0-1.268-.236c-.671 0-.972.269-.98.531 0 .29.365.48.96.762.98.44 1.435.979 1.428 1.681-.014 1.28-1.176 2.108-2.96 2.108-.764-.007-1.5-.158-1.898-.328l.238-1.386.224.099c.553.23.917.328 1.596.328.49 0 1.015-.19 1.022-.604 0-.27-.224-.466-.882-.769-.644-.295-1.505-.788-1.491-1.674C11.878 5.84 13.06 5 14.74 5c.658 0 1.19.138 1.526.263Zm2.26 3.834h1.415c-.07-.308-.392-1.786-.392-1.786l-.12-.531c-.083.23-.23.604-.223.59l-.68 1.727Zm2.1-3.985L22 11.284h-1.575s-.154-.71-.203-.926h-2.184l-.357.926h-1.785l2.527-5.66c.175-.4.483-.512.889-.512h1.316Z"
              fill="#1434CB"
            ></path>
          </g>
        </svg>
      </div>
      <div className="p-CardBrandIcons-item" data-testid="mastercard">
        <svg
          viewBox="0 0 24 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          role="presentation"
          focusable="false"
          className="w-8"
        >
          <rect fill="#252525" height="16" rx="2" width="24"></rect>
          <circle cx="9" cy="8" fill="#eb001b" r="5"></circle>
          <circle cx="15" cy="8" fill="#f79e1b" r="5"></circle>
          <path
            d="M12 4c1.214.912 2 2.364 2 4s-.786 3.088-2 4c-1.214-.912-2-2.364-2-4s.786-3.088 2-4z"
            fill="#ff5f00"
          ></path>
        </svg>
      </div>
      <div className="p-CardBrandIcons-item" data-testid="amex">
        <svg
          viewBox="0 0 24 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          role="presentation"
          focusable="false"
          className="w-8"
        >
          <rect fill="#016fd0" height="16" rx="2" width="24"></rect>
          <path
            d="M13.764 13.394V7.692l10.148.01v1.574l-1.173 1.254 1.173 1.265v1.608h-1.873l-.995-1.098-.988 1.102z"
            fill="#fffffe"
          ></path>
          <path
            d="M14.442 12.769v-4.45h3.772v1.026h-2.55v.695h2.49v1.008h-2.49v.684h2.55v1.037z"
            fill="#016fd0"
          ></path>
          <path
            d="m18.195 12.769 2.088-2.227-2.088-2.222h1.616l1.275 1.41 1.28-1.41h1.546v.035l-2.043 2.187 2.043 2.164v.063H22.35l-1.298-1.424-1.285 1.424z"
            fill="#016fd0"
          ></path>
          <path
            d="M14.237 2.632h2.446l.86 1.95v-1.95h3.02l.52 1.462.523-1.462h2.306v5.701H11.725z"
            fill="#fffffe"
          ></path>
          <g fill="#016fd0">
            <path d="m14.7 3.251-1.974 4.446h1.354l.373-.89h2.018l.372.89h1.387L16.265 3.25zm.17 2.558.592-1.415.592 1.415z"></path>
            <path d="M18.212 7.696V3.25l1.903.006.98 2.733.985-2.74h1.832v4.446l-1.179.01V4.653L21.62 7.696h-1.075l-1.136-3.054v3.054z"></path>
          </g>
        </svg>
      </div>
      <div
        className="p-CardBrandIcons-item p-CardBrandIcons-more"
        data-testid="jcb"
      >
        <svg
          viewBox="0 0 24 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          role="presentation"
          focusable="false"
          className="w-8"
        >
          <path
            d="M0 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0H2.4C1.308 0 0 1.195 0 3.2V16Z"
            fill="#047AB1"
          ></path>
          <path
            d="M2.724 10.816c-.922 0-1.838-.115-2.724-.341V9.3c.687.378 1.473.591 2.28.619.924 0 1.44-.576 1.44-1.365V5.333H6v3.222c0 1.258-.744 2.261-3.276 2.261Z"
            fill="#fff"
          ></path>
          <path
            d="M8.4 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0h-4.8C9.708 0 8.4 1.195 8.4 3.2V16Z"
            fill="#D42D06"
          ></path>
          <path
            d="M8.4 6.08c.696-.597 1.896-.97 3.84-.885 1.056.042 2.16.32 2.16.32v1.184a5.313 5.313 0 0 0-2.076-.608C10.848 5.973 9.948 6.709 9.948 8c0 1.29.9 2.027 2.376 1.92a5.387 5.387 0 0 0 2.076-.619v1.174s-1.104.288-2.16.33c-1.944.086-3.144-.288-3.84-.885V6.08Z"
            fill="#fff"
          ></path>
          <path
            d="M16.8 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0h-4.8c-1.092 0-2.4 1.195-2.4 3.2V16Z"
            fill="#67B637"
          ></path>
          <path
            d="M22.8 9.28c0 .853-.744 1.387-1.74 1.387H16.8V5.333h3.876l.276.011c.876.043 1.524.501 1.524 1.29 0 .62-.444 1.153-1.248 1.28v.033C22.116 8 22.8 8.5 22.8 9.28Zm-3.06-3.104a1.226 1.226 0 0 0-.156-.01h-1.44v1.343h1.596c.3-.064.552-.309.552-.672a.657.657 0 0 0-.552-.661Zm.18 2.176a1.16 1.16 0 0 0-.192-.01h-1.584v1.46h1.584l.192-.02a.716.716 0 0 0 .552-.715c0-.374-.24-.64-.552-.715Z"
            fill="#fff"
          ></path>
        </svg>
      </div>
    </div>
  );
};

export default function PaymentModal({
  open,
  onClose,
  title = "Payment",
  children,
  maskClosable = true,
  closable = true,
  width = "min(100vw, 80vw)",
  className = "",
}: PaymentModalProps) {
  const [form] = Form.useForm();
  const [cardNumber, setCardNumber] = useState("");
  const [countrySearch, setCountrySearch] = useState("");

  const formatCardNumber = (value: any) => {
    // Remove all non-digit characters
    const digits = value.replace(/\D/g, "");
    // Add spaces every 4 digits
    const formatted = digits.replace(/(\d{4})/g, "$1 ").trim();
    // Limit to 16 digits + 3 spaces
    return formatted.slice(0, 19);
  };

  const handleCardNumberChange = (e: any) => {
    const formattedValue = formatCardNumber(e.target.value);
    setCardNumber(formattedValue);
  };

  const onFinish = (values: any[]) => {
    console.log("Received values of form: ", values);
    // Handle payment processing here
  };

  const filterOption = (input: any, option: any) => {
    return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  const filteredCountries = countryOptions.filter((country) =>
    country.name.toLowerCase().includes(countrySearch.toLowerCase())
  );

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null} // 🔥 no footer, actions belong in children
      closable={closable}
      maskClosable={maskClosable}
      centered
      width={width}
      destroyOnHidden
      styles={{ content: { padding: 0 } }}
      classNames={{
        content: `rounded-2xl text-white shadow-2xl ${className}`,
        header: "hidden", // hide default header (we render our own if needed)
        mask: "backdrop-blur-sm",
      }}
    >
      {/* Custom header */}
      {title !== undefined && title !== null && (
        <div className="px-4">
          <h3 className="text-base font-semibold leading-6">{title}</h3>
        </div>
      )}

      {/* Body (put your buttons/content here) */}
      <div className="px-4 py-5 pb-4">
        <Form form={form} name="payment" onFinish={onFinish} layout="vertical">
          {/* Card Number Field */}
          <Form.Item
            label="Số thẻ"
            name="cardNumber"
            rules={[
              { required: true, message: "Vui lòng nhập số thẻ!" },
              {
                pattern: /^\d{4} \d{4} \d{4} \d{4}$/,
                message: "Số thẻ phải có định dạng 1234 1234 1234 1234",
              },
            ]}
          >
            <Input
              size="large"
              placeholder="1234 1234 1234 1234"
              value={cardNumber}
              onChange={handleCardNumberChange}
              maxLength={19}
              prefix={<CreditCardOutlined className="text-gray-400" />}
              suffix={<CardIcons />}
              className="rounded-md"
            />
          </Form.Item>

          <div className="flex gap-4 mb-4">
            {/* Expiration Date Field */}
            <Form.Item
              label="Ngày hết hạn"
              name="expiry"
              rules={[
                { required: true, message: "Vui lòng chọn ngày hết hạn!" },
                {
                  pattern: /^(0[1-9]|1[0-2])\/\d{2}$/,
                  message: "Định dạng phải là MM/YY",
                },
              ]}
              className="flex-1"
            >
              <Input
                size="large"
                placeholder="MM / YY"
                maxLength={5}
                className="rounded-md"
              />
            </Form.Item>

            {/* Security Code Field */}
            <Form.Item
              label="Mã bảo mật"
              name="cvc"
              rules={[
                { required: true, message: "Vui lòng nhập mã bảo mật!" },
                {
                  pattern: /^\d{3,4}$/,
                  message: "Mã bảo mật phải có 3 hoặc 4 chữ số",
                },
              ]}
              className="flex-1"
            >
              <Input
                size="large"
                placeholder="CVC"
                maxLength={4}
                prefix={<LockOutlined className="text-gray-400" />}
                className="rounded-md"
              />
            </Form.Item>
          </div>

          {/* Country Field */}
          <Form.Item
            label="Quốc gia"
            name="country"
            rules={[{ required: true, message: "Vui lòng chọn quốc gia!" }]}
          >
            <Select
              showSearch
              size="large"
              placeholder="Chọn quốc gia"
              filterOption={filterOption}
              onSearch={setCountrySearch}
              suffixIcon={<GlobalOutlined className="text-gray-400" />}
              className="rounded-lg"
            >
              {filteredCountries.map((country) => (
                <Select.Option key={country.value} value={country.value}>
                  {country.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {/* Terms Notice */}
          <div className="bg-blue-50 p-4 rounded-md text-sm text-gray-600 mb-6">
            Khi cung cấp thông tin thẻ, bạn cho phép FreeConvert.com tính phí
            thẻ của bạn cho các khoản thanh toán trong tương lai theo các điều
            khoản của họ.
          </div>

          {/* Submit Button */}
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              size="large"
              className="w-full bg-blue-600 hover:bg-blue-700 rounded-md h-12 text-lg font-medium"
            >
              Thanh toán
            </Button>
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
}
