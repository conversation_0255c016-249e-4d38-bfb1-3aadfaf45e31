"use client";

import React from "react";
import { Modal } from "antd";

export type MobileModalProps = {
  open: boolean;
  onClose: () => void;
  /** Header text (optional). Pass `null` to hide space */
  title?: React.ReactNode | null;
  /** Body content (put your own buttons here) */
  children?: React.ReactNode;
  /** Misc */
  maskClosable?: boolean;
  closable?: boolean; // default false
  width?: number | string; // default: min(92vw, 420px)
  className?: string;
  bodyClassName?: string;
};

/**
 * MobileModal — Footerless AntD modal for mobile UI.
 * - No default footer; put action buttons inside `children` for full control.
 * - Dark/purple style to match the app.
 */
export default function MobileModal({
  open,
  onClose,
  title,
  children,
  maskClosable = true,
  closable = false,
  width = "min(92vw, 420px)",
  className = "",
  bodyClassName = "",
}: MobileModalProps) {
  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null} // 🔥 no footer, actions belong in children
      closable={closable}
      maskClosable={maskClosable}
      centered
      width={width}
      destroyOnHidden
      styles={{ content: { padding: 0 } }}
      classNames={{
        content: `rounded-2xl !bg-primary text-white shadow-2xl ${className}`,
        header: "hidden", // hide default header (we render our own if needed)
        body: `pt-4 ${bodyClassName}`,
        mask: "backdrop-blur-sm",
      }}
    >
      {/* Custom header */}
      {title !== undefined && title !== null && (
        <div className="px-4">
          <h3 className="text-base font-semibold leading-6">{title}</h3>
        </div>
      )}

      {/* Body (put your buttons/content here) */}
      <div className="px-4 py-5 pb-4">{children}</div>
    </Modal>
  );
}

// --- Examples ---
// 1) EditName (single OK inside body)
// <MobileModal open={open} onClose={() => setOpen(false)} title={null}>
//   <p className="text-base font-semibold line-clamp-2">{newTitle}</p>
//   <button
//     onClick={() => setOpen(false)}
//     className="mt-4 w-full rounded-full bg-white/20 hover:bg-white/25 text-white font-semibold py-3"
//   >OK</button>
// </MobileModal>
//
// 2) Save (two buttons, free layout)
