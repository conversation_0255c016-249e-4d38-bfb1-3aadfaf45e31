/* CSS Module chỉ cho effects, animations đặc biệt */

/* Loading spinner animation */
.loadingSpinner {
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Parallax container */
.parallaxContainer {
  perspective: 1000px;
}

.parallaxImage {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.parallaxContainer:hover .parallaxImage {
  transform: rotateX(5deg) rotateY(5deg) scale(1.05);
}

/* Image overlay effects */
.imageOverlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.1) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  pointer-events: none;
}

.overlayGradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3) 0%, transparent 100%);
  pointer-events: none;
}

/* Shine effect on hover */
.shineEffect {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  transition: left 0.5s ease;
  pointer-events: none;
}

.parallaxContainer:hover .shineEffect {
  left: 100%;
}

/* Image fade-in animation */
.parallaxImage {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(1.1);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .parallaxContainer:hover .parallaxImage {
    transform: scale(1.02);
  }
}
