import './style.css'
import "./components/button.js";
import "./components/nav.js";
import "./components/layout.js";

const form = document.getElementById("userForm");
const output = document.getElementById("output");

form.addEventListener("submit", (e) => {
    e.preventDefault();
    const formData = new FormData(form);
    output.textContent = `👉 Submitted name: ${formData.get("name")}`;
});

form.addEventListener("reset", () => {
    output.textContent = "👉 Form reset!";
});
//
//
// document.querySelector('#app').innerHTML = `
//   <div>
//     <a href="https://vite.dev" target="_blank">
//       <img src="${viteLogo}" class="logo" alt="Vite logo" />
//     </a>
//     <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript" target="_blank">
//       <img src="${javascriptLogo}" class="logo vanilla" alt="JavaScript logo" />
//     </a>
//     <h1>Hello Vite!</h1>
//     <div class="card">
//       <button id="counter" type="button"></button>
//     </div>
//     <p class="read-the-docs">
//       Click on the Vite logo to learn more
//     </p>
//   </div>
// `
//
// setupCounter(document.querySelector('#counter'))
