"use client";

import { useState, useCallback, memo } from "react";
import {
  MenuOutlined,
  AppstoreOutlined,
  FilterOutlined,
  VideoCameraOutlined,
} from "@ant-design/icons";
import SortModal from "../SortModal";
import { useViewMode } from "@/contexts/ViewModeContext";
import { useTab } from "@/contexts/TabContext";
import { useFolder } from "@/contexts/FolderContext";
import { useCompressedView } from "@/contexts/CompressedViewContext";

export default function Header({
  onToggleSidebar,
}: {
  onToggleSidebar: () => void;
}) {
  const [sortModalOpen, setSortModalOpen] = useState(false);
  const { viewMode, setViewMode } = useViewMode();
  const { activeTab, setActiveTab } = useTab();
  const { currentFolder, setCurrentFolder, folderName } = useFolder();
  const { compressedViewMode, setCompressedViewMode } = useCompressedView();

  // Optimize các handlers với useCallback
  const handleViewModeToggle = useCallback(() => {
    if (activeTab === "COMPRESSED") {
      const newMode = compressedViewMode === "grid" ? "list" : "grid";
      setCompressedViewMode(newMode);
    } else {
      const newMode = viewMode === "list" ? "grid" : "list";
      setViewMode(newMode);
    }
  }, [activeTab, compressedViewMode, viewMode, setCompressedViewMode, setViewMode]);

  const handleCompressedListToggle = useCallback(() => {
    const newMode = compressedViewMode === "grid" ? "list" : "grid";
    setCompressedViewMode(newMode);
  }, [compressedViewMode, setCompressedViewMode]);

  const handleBackToFolders = useCallback(() => {
    setCurrentFolder(null);
  }, [setCurrentFolder]);

  // Optimize tab onClick handlers
  const handleAllTabClick = useCallback(() => setActiveTab("ALL"), [setActiveTab]);
  const handleFolderTabClick = useCallback(() => setActiveTab("FOLDER"), [setActiveTab]);
  const handleCompressedTabClick = useCallback(() => setActiveTab("COMPRESSED"), [setActiveTab]);

  return (
    <header className="sticky top-0 z-40 text-white bg-gradient-to-b from-primary-2 to-primary">
      <div className="h-16 flex items-center justify-between px-[13px] ">
        {/* Left: Toggle + Title */}
        <div className="flex items-center gap-3 min-w-0">
          {currentFolder && activeTab === "FOLDER" ? (
            /* Back button when in folder view */
            <button
              onClick={handleBackToFolders}
              aria-label="Back to folders"
              className="grid place-items-center size-9 rounded-xl hover:bg-white/10 transition"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          ) : (
            /* Menu button when not in folder view */
            <button
              onClick={onToggleSidebar}
              aria-label="Open sidebar"
              className="grid place-items-center size-9 rounded-xl hover:bg-white/10 transition"
            >
              <MenuOutlined className="text-base max-[376px]:text-sm" />
            </button>
          )}
          <p className="font-medium text-lg ">
            {currentFolder && activeTab === "FOLDER" ? folderName : "Rocket Converter"}
          </p>
        </div>

        {/* Right: icons */}
        <div className="flex items-center gap-2">
          <button 
            onClick={handleCompressedListToggle}
            className="grid place-items-center size-9 rounded-xl hover:bg-white/10 transition"
          >
            <VideoCameraOutlined className="text-xl max-[376px]:text-base" />
          </button>
          <button 
            onClick={handleViewModeToggle}
            className="grid place-items-center size-9 rounded-xl hover:bg-white/10 transition"
          >
            <AppstoreOutlined className="text-xl max-[376px]:text-base" />
          </button>
          <button onClick={() => setSortModalOpen(true)} className="grid place-items-center size-9 rounded-xl hover:bg-white/10 transition">
            <FilterOutlined className="text-xl max-[376px]:text-base" />
          </button>
        </div>
      </div>

      {/* Pills */}
      <div className="px-4 pb-3">
        <div className="flex items-center gap-2 ">
          <TabPill
            label="ALL VIDEO"
            active={activeTab === "ALL"}
            onClick={handleAllTabClick}
          />
          <TabPill
            label="FOLDER"
            active={activeTab === "FOLDER"}
            onClick={handleFolderTabClick}
          />
          <TabPill
            label="COMPRESSED"
            active={activeTab === "COMPRESSED"}
            onClick={handleCompressedTabClick}
          />
        </div>
      </div>
      <SortModal open={sortModalOpen} onClose={() => setSortModalOpen(false)} />
    </header>
  );
}

// Optimize TabPill với React.memo
const TabPill = memo(function TabPill({
  label,
  active,
  onClick,
}: {
  label: string;
  active?: boolean;
  onClick?: () => void;
}) {
  return (
    <button
      onClick={onClick}
      className={[
        "px-4 py-2 rounded-full text-base font-medium transition max-[376px]:text-sm max-[376px]:px-2 max-[376px]:py-1 max-[376px]:rounded-full max-[376px]:w-full",
        active
          ? "bg-white text-primary"
          : "bg-primary text-[var(--color-text-primary)]",
      ].join(" ")}
      aria-pressed={active}
    >
      {label}
    </button>
  );
});
