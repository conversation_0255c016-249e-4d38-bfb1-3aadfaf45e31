"use client";

import { useState } from "react";
import {
  MenuOutlined,
  AppstoreOutlined,
  FilterOutlined,
  VideoCameraOutlined,
} from "@ant-design/icons";
import SortModal from "../SortModal";
import { useViewMode } from "@/contexts/ViewModeContext";
import { useTab } from "@/contexts/TabContext";
import { useFolder } from "@/contexts/FolderContext";
import { useCompressedView } from "@/contexts/CompressedViewContext";

export default function Header({
  onToggleSidebar,
}: {
  onToggleSidebar: () => void;
}) {
  const [sortModalOpen, setSortModalOpen] = useState(false);
  const { viewMode, setViewMode } = useViewMode();
  const { activeTab, setActiveTab } = useTab();
  const { currentFolder, setCurrentFolder, folderName } = useFolder();
  const { compressedViewMode, setCompressedViewMode } = useCompressedView();

  const handleViewModeToggle = () => {
    if (activeTab === "COMPRESSED") {
      const newMode = compressedViewMode === "grid" ? "list" : "grid";
      setCompressedViewMode(newMode);
    } else {
      const newMode = viewMode === "list" ? "grid" : "list";
      setViewMode(newMode);
    }
  };

  const handleCompressedListToggle = () => {
    const newMode = compressedViewMode === "grid" ? "list" : "grid";
    setCompressedViewMode(newMode);
  };

  const handleBackToFolders = () => {
    setCurrentFolder(null);
  };

  return (
    <header className="sticky top-0 z-40 text-white bg-gradient-to-b from-primary-2 to-primary">
      <div className="h-16 flex items-center justify-between px-[13px] ">
        {/* Left: Toggle + Title */}
        <div className="flex items-center gap-3 min-w-0">
          {currentFolder && activeTab === "FOLDER" ? (
            /* Back button when in folder view */
            <button
              onClick={handleBackToFolders}
              aria-label="Back to folders"
              className="grid place-items-center size-9 rounded-xl hover:bg-white/10 transition"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          ) : (
            /* Menu button when not in folder view */
            <button
              onClick={onToggleSidebar}
              aria-label="Open sidebar"
              className="grid place-items-center size-9 rounded-xl hover:bg-white/10 transition"
            >
              <MenuOutlined className="text-xl" />
            </button>
          )}
          <p className="font-medium text-lg ">
            {currentFolder && activeTab === "FOLDER" ? folderName : "Rocket Converter"}
          </p>
        </div>

        {/* Right: icons */}
        <div className="flex items-center gap-2">
          <button 
            onClick={handleCompressedListToggle}
            className="grid place-items-center size-9 rounded-xl hover:bg-white/10 transition"
          >
            <VideoCameraOutlined className="text-xl" />
          </button>
          <button 
            onClick={handleViewModeToggle}
            className="grid place-items-center size-9 rounded-xl hover:bg-white/10 transition"
          >
            <AppstoreOutlined className="text-xl" />
          </button>
          <button onClick={() => setSortModalOpen(true)} className="grid place-items-center size-9 rounded-xl hover:bg-white/10 transition">
            <FilterOutlined className="text-xl" />
          </button>
        </div>
      </div>

      {/* Pills */}
      <div className="px-4 pb-3">
        <div className="flex items-center gap-2 ">
          <TabPill
            label="ALL VIDEO"
            active={activeTab === "ALL"}
            onClick={() => setActiveTab("ALL")}
          />
          <TabPill
            label="FOLDER"
            active={activeTab === "FOLDER"}
            onClick={() => setActiveTab("FOLDER")}
          />
          <TabPill
            label="COMPRESSED"
            active={activeTab === "COMPRESSED"}
            onClick={() => setActiveTab("COMPRESSED")}
          />
        </div>
      </div>
      <SortModal open={sortModalOpen} onClose={() => setSortModalOpen(false)} />
    </header>
  );
}

function TabPill({
  label,
  active,
  onClick,
}: {
  label: string;
  active?: boolean;
  onClick?: () => void;
}) {
  return (
    <button
      onClick={onClick}
      className={[
        "px-4 py-[9px] rounded-full text-lg font-medium  transition ",
        active
          ? "bg-white text-[#4E2EAF] "
          : "bg-[#4020A1] text-[#C3AFFF] hover:bg-white/20 hover:text-[#6A11CB]",
      ].join(" ")}
      aria-pressed={active}
    >
      {label}
    </button>
  );
}
