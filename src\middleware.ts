import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  const ua = request.headers.get("user-agent") || "";
  // Regex nhận diện mobile device
  const isMobile =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua);
  const url = request.nextUrl.clone();

  const startsWithMobile = url.pathname.startsWith("/mobile");
  if (isMobile) {
    if (!startsWithMobile) {
      url.pathname = "/mobile";
      const response = NextResponse.redirect(url);
      // response.headers.set('x-is-mobile', "true");
      return response;
    }
  } else if (startsWithMobile) {
    url.pathname = "/";
    return NextResponse.redirect(url);
  }
  return NextResponse.next();
}

// Áp dụng middleware cho tất cả route
export const config = {
  matcher: ["/((?!_next|favicon.ico|assets).*)"],
};
