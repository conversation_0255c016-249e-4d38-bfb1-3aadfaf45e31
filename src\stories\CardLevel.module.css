/* CSS Module chỉ cho animations và effects đặc biệt */

/* Animated level badge */
.animatedLevel {
  animation: levelFloat 3s ease-in-out infinite;
}

@keyframes levelFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

.levelBadge {
  transition: all 0.3s ease;
  overflow: hidden;
}

.levelBadge:hover {
  transform: scale(1.05);
}

/* Max level effects */
.maxLevel .levelBadge {
  background: linear-gradient(45deg, #fbbf24, #f59e0b, #fbbf24);
  background-size: 200% 200%;
  animation: maxLevelGlow 2s ease-in-out infinite;
}

@keyframes maxLevelGlow {
  0% {
    background-position: 0% 50%;
    box-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
  }
  50% {
    background-position: 100% 50%;
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.8);
  }
  100% {
    background-position: 0% 50%;
    box-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
  }
}

.maxText {
  animation: maxTextPulse 1.5s ease-in-out infinite;
}

@keyframes maxTextPulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

/* Progress bar */
.progressContainer {
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0 0 4px 4px;
  overflow: hidden;
}

.progressBar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: width 0.5s ease;
  animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

/* Sparkle overlay for max level */
.sparkleOverlay {
  position: absolute;
  inset: 0;
  background-image: radial-gradient(1px 1px at 10px 5px, #fff, transparent),
    radial-gradient(1px 1px at 20px 10px, #fff, transparent),
    radial-gradient(1px 1px at 30px 5px, #fff, transparent);
  background-size: 40px 15px;
  animation: sparkleMove 2s linear infinite;
  pointer-events: none;
  opacity: 0.7;
}

@keyframes sparkleMove {
  0% {
    transform: translateX(-40px);
  }
  100% {
    transform: translateX(40px);
  }
}

/* Level up effect */
.levelUpEffect {
  position: absolute;
  inset: -10px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(34, 197, 94, 0.3) 0%,
    transparent 70%
  );
  animation: levelUpPulse 0.6s ease-out;
  pointer-events: none;
  opacity: 0;
}

@keyframes levelUpPulse {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Hover enhancements */
.withProgress:hover .progressBar {
  background: linear-gradient(90deg, #22c55e, #16a34a);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .levelBadge {
    font-size: 0.7rem;
    padding: 2px 6px;
  }

  .progressContainer {
    height: 1px;
  }
}
