"use client";
import { useMemo, useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
// import MobileLayout from "@/mobile/layout.rm";
import FooterActionBar from "@/components/mobile/footer/FooterActionBar";

import { VideoGroup, VideoItemModel } from "@/components/mobile/video/type";
import { useViewMode } from "@/contexts/ViewModeContext";
import { useTab } from "@/contexts/TabContext";
import { useFolder } from "@/contexts/FolderContext";
import { useCompressedView } from "@/contexts/CompressedViewContext";
import VideoList from "@/components/mobile/video/VideoList";
import FolderVideoView from "@/components/mobile/folder/FolderVideoView";
import FolderList from "@/components/mobile/folder/FolderList";
import CompressedVideoView from "@/components/mobile/compressed/CompressedVideoView";

function formatBytes(n: number): string {
  if (n < 1024) return `${n}B`;
  const u = ["KB", "MB", "GB", "TB"];
  let i = -1;
  do {
    n /= 1024;
    i++;
  } while (n >= 1024 && i < u.length - 1);
  return `${Math.round(n)}${u[i]}`;
}

const groupsMock: VideoGroup[] = [
  /* ... dữ liệu như trước ... */
];

export default function VideosPage() {
  const router = useRouter();
  const [selectedVideos, setSelectedVideos] = useState<Set<number>>(new Set());
  const [selectedCompressedVideo, setSelectedCompressedVideo] = useState<
    number | null
  >(null);
  const { viewMode } = useViewMode();
  const { activeTab } = useTab();
  const { currentFolder, setCurrentFolder, setFolderName } = useFolder();
  const { compressedViewMode } = useCompressedView();

  const handleVideoSelect = (videoId: number) => {
    setSelectedVideos((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(videoId)) {
        newSet.delete(videoId);
      } else {
        newSet.add(videoId);
      }
      return newSet;
    });
  };

  const selectedCount = selectedVideos.size;

  // Tính tổng size của videos được chọn (giả sử mỗi video 32MB)
  const totalBytes = useMemo(() => {
    return selectedCount * 32 * 1024 * 1024; // 32MB per video
  }, [selectedCount]);

  const totalSizeLabel = selectedCount ? formatBytes(totalBytes) : "0B";

  const handleFolderSelect = (folderId: string, folderName: string) => {
    setCurrentFolder(folderId);
    setFolderName(folderName);
    setSelectedVideos(new Set()); // Clear selections when entering folder
  };

  const handleCompressedVideoSelect = (videoId: number) => {
    setSelectedCompressedVideo((prevSelected) =>
      prevSelected === videoId ? null : videoId
    );
  };

  const handleNext = (tool: "convert" | "trim" | "crop" | "rotation") => {
    if (selectedCount === 0) return;

    const selectedVideoIds = Array.from(selectedVideos);
    if (selectedVideoIds.length === 0) return;

    // Lấy video đầu tiên để làm slug (có thể thay đổi logic này)
    const firstVideoId = selectedVideoIds[0];

    switch (tool) {
      case "trim":
        router.push(`/mobile/trim/${firstVideoId}`);
        break;
      case "convert":
        router.push(`/mobile/convert/${firstVideoId}`);
        break;
      case "crop":
        router.push(`/mobile/crop/${firstVideoId}`);
        break;
      case "rotation":
        router.push(`/mobile/rotation/${firstVideoId}`);
        break;
      default:
        console.log("Unknown tool:", tool);
    }
  };

  const handleCompressedNext = (
    tool: "convert" | "trim" | "crop" | "rotation"
  ) => {
    if (!selectedCompressedVideo) return;

    switch (tool) {
      case "trim":
        router.push(`/mobile/trim/${selectedCompressedVideo}`);
        break;
      case "convert":
        router.push(`/mobile/convert/${selectedCompressedVideo}`);
        break;
      case "crop":
        router.push(`/mobile/crop/${selectedCompressedVideo}`);
        break;
      case "rotation":
        router.push(`/mobile/rotation/${selectedCompressedVideo}`);
        break;
      default:
        console.log("Unknown tool:", tool);
    }
  };

  // Optimize renderContent với useMemo
  const renderContent = useMemo(() => {
    switch (activeTab) {
      case "FOLDER":
        return currentFolder ? (
          <FolderVideoView
            folderId={currentFolder}
            selectedVideos={selectedVideos}
            onVideoSelect={handleVideoSelect}
          />
        ) : (
          <FolderList onFolderSelect={handleFolderSelect} />
        );
      case "COMPRESSED":
        return <CompressedVideoView viewMode={compressedViewMode} />;
      case "ALL":
      default:
        return (
          <VideoList
            viewMode={viewMode}
            selectedVideos={selectedVideos}
            onVideoSelect={handleVideoSelect}
          />
        );
    }
  }, [activeTab, currentFolder, selectedVideos, viewMode, compressedViewMode]);

  return (
    <div className="w-full relative min-h-screen bg-neutral-900 text-white">
      <div className="">{renderContent}</div>

      {(activeTab === "ALL" || (activeTab === "FOLDER" && currentFolder)) && (
        <FooterActionBar
          open={selectedCount > 0}
          selectedCount={selectedCount}
          totalSizeLabel={totalSizeLabel}
          onClear={() => setSelectedVideos(new Set())}
          onNext={handleNext}
        />
      )}

      {activeTab === "COMPRESSED" && selectedCompressedVideo && (
        <FooterActionBar
          open={true}
          selectedCount={1}
          totalSizeLabel="32MB"
          onClear={() => setSelectedCompressedVideo(null)}
          onNext={handleCompressedNext}
        />
      )}
    </div>
  );
}