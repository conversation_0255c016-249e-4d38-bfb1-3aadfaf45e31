"use client";
import React, { useState } from "react";
import Image from "next/image";
import EditNamePopup from "@/components/Shared/popup/EditNamePopup";

export type VideoInfoCardProps = {
  thumbnail: string;
  title: string;
  original: { size: string; resolution?: string };
  compressed: { size: string; duration?: string };
  /** Optional link target for the pencil icon. Prefer this over passing handlers from a Server Component. */
  editHref?: string;
  className?: string;
};

const StatBlock = ({
  heading,
  lines,
  align = "left",
}: {
  heading: string;
  lines: (string | undefined)[];
  align?: "left" | "right";
}) => (
  <div className={`flex flex-col gap-4 items-start`}>
    <span className="text-sm font-semibold text-white/90">{heading}</span>
    {lines.filter(Boolean).map((t, i) => (
      <span key={i} className="text-base text-white">
        {t}
      </span>
    ))}
  </div>
);

/**
 * VideoInfoCard
 * - Dark, compact card like the provided mock
 * - Left thumbnail, middle 'Original', right 'Compressed'
 * - Bottom title row with optional pencil link
 */
export default function VideoInfoCard({
  thumbnail,
  title,
  original,
  compressed,
  editHref,
  className = "",
}: VideoInfoCardProps) {
  const [open, setOpen] = useState(false);
  const toggleEditPopup = () => {
    setOpen(!open);
  };

  return (
    <div
      className={`w-full rounded-xl overflow-hidden bg-neutral-900 text-white shadow-xl ${className}`}
    >
      {/* Top info strip */}
      <div className="flex gap-3 p-2 bg-dark-gray-1">
        {/* Thumbnail */}
        <div className="h-[120px] w-[178px] overflow-hidden relative">
          <Image
            src={thumbnail}
            alt={title}
            className="h-full w-full object-cover"
            loading="lazy"
            fill
          />
        </div>

        <div className="flex-1 flex justify-between">
          <StatBlock
            heading="Original"
            lines={[original.size, original.resolution]}
            align="left"
          />

          {/* Compressed stats */}
          <StatBlock
            heading="Compressed"
            lines={[compressed.size, compressed.duration]}
            align="right"
          />
        </div>
      </div>

      {/* Bottom title row */}
      <div className="flex items-center justify-between bg-dark-gray-2 px-3 py-2">
        <div className="min-w-0 pr-3">
          <span className="block truncate text-18">{title}</span>
        </div>

        <button className="relative" onClick={toggleEditPopup}>
          <Image
            src="/assets/svg/pencil.svg"
            alt="Save"
            width={19.5}
            height={19.5}
          />
        </button>
      </div>
      <EditNamePopup
        open={open}
        content={"Hội cựu sinh viên ĐH QG-edti title "}
        onClose={toggleEditPopup}
      />
    </div>
  );
}

// --- Example usage ---
// <VideoInfoCard
//   thumbnail="/images/thumb.jpg"
//   title="Hội cựu sinh viên ĐH QG"
//   original={{ size: "32MB", resolution: "1280x720" }}
//   compressed={{ size: "17MB", duration: "00:00:05" }}
//   editHref="/mobile/trim/123/edit"
// />
