import type { Metada<PERSON> } from "next";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import { Kanit } from "next/font/google";
import "@/mobile/globals.mb.css";
import Shell from "@/components/mobile/Shell";
import { ViewModeProvider } from "@/contexts/ViewModeContext";
import { TabProvider } from "@/contexts/TabContext";
import { FolderProvider } from "@/contexts/FolderContext";
import { CompressedViewProvider } from "@/contexts/CompressedViewContext";

const kanit = Kanit({
  subsets: ["latin"], // chọn subset cần
  weight: ["400", "500", "600", "700", "800", "900"], // chọn độ đậm
  variable: "--font-kanit", // CSS variable
});

// export const metadata: Metadata = {
//   title: "Fast Converter Admin",
//   description: "Convert video file fast and convenient",
// };

export default function MobileLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <Shell>{children}</Shell>
    // <html lang="en">
    //   <body className={`${kanit.variable} antialiased`}>
    //     <ViewModeProvider>
    //       <TabProvider>
    //         <FolderProvider>
    //           <CompressedViewProvider>
    //           </CompressedViewProvider>
    //         </FolderProvider>
    //       </TabProvider>
    //     </ViewModeProvider>
    //   </body>
    // </html>
  );
}
