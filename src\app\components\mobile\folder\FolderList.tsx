"use client";

import Image from "next/image";

type FolderData = {
  id: string;
  name: string;
  videoCount: number;
  thumbnails: string[]; // Array of 4 thumbnail images
};

const folderData: FolderData[] = [
  {
    id: "camera",
    name: "Camera",
    videoCount: 15,
    thumbnails: ["/images/Video1.png", "/images/Video2.png", "/images/Video3.png", "/images/Video4.png"]
  },
  {
    id: "fast-convert",
    name: "Fast convert to video",
    videoCount: 8,
    thumbnails: ["/images/Video5.png", "/images/Video6.png", "/images/Video7.png", "/images/Video8.png"]
  },
  {
    id: "travel",
    name: "Travel", 
    videoCount: 6,
    thumbnails: ["/images/Video9.png", "/images/Video10.png", "/images/Video1.png", "/images/Video2.png"]
  },
  {
    id: "zalo",
    name: "<PERSON><PERSON>",
    videoCount: 5,
    thumbnails: ["/images/Video3.png", "/images/Video4.png", "/images/Video5.png", "/images/Video6.png"]
  },
  {
    id: "telegram",
    name: "Telegram",
    videoCount: 3,
    thumbnails: ["/images/Video7.png", "/images/Video8.png", "/images/Video9.png", "/images/Video10.png"]
  },
  {
    id: "imou",
    name: "Imou",
    videoCount: 4,
    thumbnails: ["/images/Video1.png", "/images/Video3.png", "/images/Video5.png", "/images/Video7.png"]
  }
];

type FolderListProps = {
  onFolderSelect?: (folderId: string, folderName: string) => void;
};

export default function FolderList({ onFolderSelect }: FolderListProps) {
  return (
    <div className="p-4 space-y-4">
      {folderData.map((folder) => (
        <div
          key={folder.id}
          onClick={() => onFolderSelect?.(folder.id, folder.name)}
          className="flex  gap-4 p-3  cursor-pointer hover:bg-neutral-700 transition-colors"
        >
          <div className="relative w-[140px] h-[95px] flex-shrink-0">
            <div className="flex gap-[1px] w-full h-full  ">
              <div className="relative w-1/2 h-full">
                <Image
                  src={folder.thumbnails[0]}
                  alt={`${folder.name} thumbnail 1`}
                  fill
                  className="object-cover w-[70px] h-[95px]"
                />
              </div>
              
              <div className="flex flex-col gap-[1px] w-1/2 h-full">
                <div className="relative h-1/2">
                  <Image
                    src={folder.thumbnails[1]}
                    alt={`${folder.name} thumbnail 2`}
                    fill
                    className="object-cover w-[70px] h-[47px]"
                  />
                </div>
                <div className="relative h-1/2">
                  <Image
                    src={folder.thumbnails[2]}
                    alt={`${folder.name} thumbnail 3`}
                    fill
                    className="object-cover w-[70px] h-[47px]"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Folder info */}
          <div className="flex-1 min-w-0 flex flex-col justify-between">
            <h3 className="font-medium text-[18px] leading-[100%] tracking-[0]">
              {folder.name}
            </h3>
            <p className="font-light text-[15px] leading-[100%] tracking-[0] text-[#C0BFBF]">
              {folder.videoCount} video
            </p>
          </div>

          {/* Arrow icon */}
          <div className="text-gray-400 flex items-center justify-center">
            <svg 
              className="w-5 h-5" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M9 5l7 7-7 7" 
              />
            </svg>
          </div>
        </div>
      ))}
    </div>
  );
}
