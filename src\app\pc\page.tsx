"use client";
import React from "react";
import "@/pc/globals.pc.css";
import HomeBanner from "@/components/pc/banner/HomeBanner";
import {
  CarOutlined,
  ClockCircleOutlined,
  CloudOutlined,
  DownOutlined,
  SafetyOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { But<PERSON> } from "antd";
import FeatureCard from "@/components/pc/card/FeatureCard";
import DragUploadBox from "@/components/pc/upload/DragUploadBox";
import Feat1IconSVG from "@/components/pc/svg/home/<USER>";
import Feat2IconSVG from "@/components/pc/svg/home/<USER>";
import Feat3IconSVG from "@/components/pc/svg/home/<USER>";
import Feat4IconSVG from "@/components/pc/svg/home/<USER>";

const HomePage = () => {
  return (
    <div className="home-page">
      <HomeBanner />
      {/* Heading */}
      <div className="mt-8 text-center">
        <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
          Fastest Online Video Converter
        </h1>
        <p className="mt-2 text-18">Convert your videos to any format</p>
      </div>
      {/* Upload box */}
      <DragUploadBox />
      {/* Feature cards */}
      <div className="mx-auto my-8 grid w-content grid-cols-1 gap-5 md:grid-cols-2 lg:grid-cols-4">
        <FeatureCard
          icon={<Feat1IconSVG />}
          title="Fast conversion"
          desc="Support converting video, audio, images, cutting, rotating videos quickly."
        />
        <FeatureCard
          icon={<Feat2IconSVG />}
          title="Completely free"
          desc="No registration, no login, no cookies, no copyright and unlimited usage."
        />
        <FeatureCard
          icon={<Feat3IconSVG />}
          title="Works everywhere"
          desc="Online converter—works on any device and browser. Upload file and choose format."
        />
        <FeatureCard
          icon={<Feat4IconSVG />}
          title="Privacy guaranteed"
          desc="Your security and privacy are guaranteed when transferring files."
        />
      </div>
    </div>
  );
};

export default HomePage;
