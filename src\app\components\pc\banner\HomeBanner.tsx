"use client";
import React from "react";
import { Button } from "antd";
import { RightOutlined } from "@ant-design/icons";
import Image from "next/image";

const HomeBanner: React.FC = () => {
  return (
    <section
      className="
        relative w-full overflow-hidden rounded-xl text-white mx-auto h-[390px]
      "
    >
      {/* Background image */}
      <Image
        src="/assets/images/Banner.png"
        alt=""
        fill
        priority
        className="object-cover"
        sizes="100vw"
      />

      {/* Gradient overlay tăng độ tương phản */}
      <div className="absolute inset-0 bg-gradient-to-r from-[#6a3df8]/80 via-[#6a3df8]/50 to-transparent" />

      {/* Content */}
      <div className="relative h-full z-10 mx-auto w-content flex flex-col gap-6 justify-center">
        <h1 className="text-2xl md:text-3xl font-semibold leading-tight">
          Convert video formats to most
          <br className="hidden md:block" /> popular formats
        </h1>

        <p className=" text-white/85 text-sm md:text-base">
          Fast convert helps you convert video formats to formats such as vp8,{" "}
          <br />
          vp9, h.264, h.265, av1 with the fastest speed on the market.
        </p>

        <div className="">
          <Button
            type="primary"
            size="large"
            shape="round"
            ghost
            className="!text-white !border-white !bg-white/10 hover:!bg-white/20 !px-6"
          >
            Convert now
            <RightOutlined className="ml-2 align-middle" />
          </Button>
        </div>
      </div>

      {/* Đặt chiều cao tối thiểu để banner luôn “đủ cao” như thiết kế */}
      <div className="pointer-events-none h-[220px] md:h-[280px]" />
    </section>
  );
};

export default HomeBanner;
