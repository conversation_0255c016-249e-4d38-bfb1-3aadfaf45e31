"use client";

import { useMemo, useState } from "react";
import type { ColumnsType } from "antd/es/table";
import {
  Table,
  Tag,
  Input,
  Button,
  Space,
  Switch,
  Modal,
  Form,
  DatePicker,
  message,
} from "antd";
import moment, { Moment } from "moment";

type User = {
  id: string;
  name: string;
  email: string;
  password: string;
  isVip: boolean;
  expired?: string; // ngày hết hạn dạng YYYY-MM-DD
};

const seed: User[] = [
  {
    id: "1",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    password: "An@123456",
    isVip: true,
    expired: "2025-12-31",
  },
  {
    id: "2",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    password: "Binh@123456",
    isVip: false,
    expired: "2024-08-01",
  }, // hết hạn
  {
    id: "3",
    name: "<PERSON><PERSON> <PERSON>",
    email: "<EMAIL>",
    password: "<PERSON>@123456",
    isVip: true,
    expired: "2026-03-15",
  },
  {
    id: "4",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    password: "<PERSON><PERSON>@123456",
    isVip: true,
    expired: "2023-11-20",
  }, // hết hạn
  {
    id: "5",
    name: "Hoàng Em",
    email: "<EMAIL>",
    password: "Em@123456",
    isVip: false,
    expired: "2025-05-01",
  },
  {
    id: "6",
    name: "Vũ Hoa",
    email: "<EMAIL>",
    password: "Hoa@123456",
    isVip: true,
    expired: "2025-08-30",
  },
  {
    id: "7",
    name: "Đỗ Khánh",
    email: "<EMAIL>",
    password: "Khanh@123456",
    isVip: false,
    expired: "2024-12-01",
  },
];

export default function AdminUsersPage() {
  const [users, setUsers] = useState<User[]>(seed);
  const [query, setQuery] = useState("");
  const [showPass, setShowPass] = useState(false);
  const [openCreate, setOpenCreate] = useState(false);
  const [form] = Form.useForm<User>();

  const filtered = useMemo(() => {
    const s = query.trim().toLowerCase();
    if (!s) return users;
    return users.filter(
      (u) =>
        u.name.toLowerCase().includes(s) || u.email.toLowerCase().includes(s)
    );
  }, [users, query]);

  const columns: ColumnsType<User> = [
    {
      title: "No",
      key: "no",
      width: 64,
      render: (_v, _r, idx) => <span className="text-sm">{idx + 1}</span>,
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render: (v) => <span className="font-medium">{v}</span>,
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      render: (v) => <span className="text-neutral-700">{v}</span>,
    },
    {
      title: (
        <div className="flex items-center justify-between pr-2">
          <span>Pass</span>
          <div className="flex items-center gap-2 text-xs text-neutral-500">
            <span>Show</span>
            <Switch
              checked={showPass}
              onChange={setShowPass}
              className="[&_.ant-switch-inner]:!hidden"
            />
          </div>
        </div>
      ),
      key: "password",
      render: (_, r) =>
        showPass ? (
          <span className="font-mono text-[13px]">{r.password}</span>
        ) : (
          <span className="tracking-widest select-none">••••••••</span>
        ),
    },
    {
      title: "Trạng thái",
      dataIndex: "isVip",
      key: "status",
      width: 140,
      render: (isVip: boolean) =>
        isVip ? (
          <Tag color="#6A11CB" className="px-3 py-1 rounded-full font-semibold">
            VIP
          </Tag>
        ) : (
          <Tag color="default" className="px-3 py-1 rounded-full font-semibold">
            Thường
          </Tag>
        ),
    },
    {
      title: "Expired Date",
      dataIndex: "expired",
      key: "expired",
      width: 160,
      render: (v) => {
        if (!v) return <span className="text-neutral-400">—</span>;
        const expired = moment(v);
        const isExpired = expired.isBefore(moment(), "day");
        return (
          <span
            className={
              isExpired
                ? "text-red-600 font-medium"
                : "text-green-600 font-medium"
            }
          >
            {expired.format("YYYY-MM-DD")}
          </span>
        );
      },
    },
  ];

  const onCreate = async () => {
    const v = await form.validateFields();
    const exists = users.some((u) => u.email === v.email);
    if (exists) return message.error("Email đã tồn tại");

    setUsers((prev) => [
      ...prev,
      {
        id: String(prev.length + 1),
        name: v.name,
        email: v.email,
        password: v.password,
        isVip: !!v.isVip,
        expired:
          v.isVip && v.expired
            ? (v.expired as unknown as Moment).format("YYYY-MM-DD")
            : undefined,
      },
    ]);
    setOpenCreate(false);
    form.resetFields();
    message.success("Tạo user thành công");
  };

  return (
    <section className="space-y-4">
      {/* Header actions */}
      <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
        <h2 className="text-xl font-semibold">User Management</h2>
        <Space className="w-full sm:w-auto">
          <Input
            allowClear
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Tìm theo tên hoặc email…"
            className="w-full sm:w-72 rounded-lg"
          />
          <Button
            type="primary"
            className="bg-gradient-to-r from-[#6A11CB] to-[#8E2DE2] font-medium"
            onClick={() => setOpenCreate(true)}
          >
            + New User
          </Button>
        </Space>
      </div>

      {/* Table */}
      <div className="rounded-xl border border-neutral-200 shadow-sm overflow-hidden">
        <Table<User>
          rowKey="id"
          columns={columns}
          dataSource={filtered}
          pagination={{ pageSize: 8, className: "px-3" }}
          className="[&_.ant-table-thead>tr>th]:uppercase [&_.ant-table-thead>tr>th]:text-xs
                     [&_.ant-table-thead>tr>th]:tracking-wide [&_.ant-table-thead>tr>th]:font-semibold
                     [&_.ant-table]:bg-white"
        />
      </div>

      {/* Create User Modal */}
      <Modal
        title="Tạo User"
        open={openCreate}
        onOk={onCreate}
        onCancel={() => setOpenCreate(false)}
        okText="Create"
        className="[&_.ant-modal-title]:font-semibold"
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="Name"
            rules={[{ required: true, message: "Nhập tên" }]}
          >
            <Input className="rounded-lg" />
          </Form.Item>
          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: "Nhập email" },
              { type: "email", message: "Email không hợp lệ" },
            ]}
          >
            <Input className="rounded-lg" />
          </Form.Item>
          <Form.Item
            name="password"
            label="Password"
            rules={[{ required: true, message: "Nhập mật khẩu" }]}
          >
            <Input.Password className="rounded-lg" />
          </Form.Item>
          <Form.Item name="isVip" label="Trạng thái" valuePropName="checked">
            <Switch className="bg-neutral-200" />{" "}
            <span className="ml-2">VIP</span>
          </Form.Item>
          <Form.Item name="expired" label="Expired Date">
            <DatePicker className="w-full rounded-lg" format="YYYY-MM-DD" />
          </Form.Item>
        </Form>
      </Modal>
    </section>
  );
}
