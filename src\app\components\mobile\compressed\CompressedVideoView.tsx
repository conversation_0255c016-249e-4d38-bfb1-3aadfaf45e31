"use client";

import Image from "next/image";

type CompressedVideoData = {
  id: number;
  title: string;
  thumbnail: string;
  size: string;
  duration: string;
};

// Data mẫu cho compressed videos
const compressedVideos: CompressedVideoData[] = [
  {
    id: 1,
    title: "Survival in the deep forest",
    thumbnail: "/assets/images/Video1.png",
    size: "32MB",
    duration: "00:00:25"
  },
  {
    id: 2,
    title: "Đại tướng <PERSON>",
    thumbnail: "/assets/images/Video2.png", 
    size: "32MB",
    duration: "00:00:25"
  },
  {
    id: 3,
    title: "Nữ quái công an",
    thumbnail: "/assets/images/Video3.png",
    size: "32MB", 
    duration: "00:00:25"
  },
  {
    id: 4,
    title: "Nữ Đại Úy",
    thumbnail: "/assets/images/Video4.png",
    size: "32MB",
    duration: "00:00:25"
  }
];

type CompressedVideoViewProps = {

  viewMode?: "grid" | "list";
};

export default function CompressedVideoView({ 
 
  viewMode = "grid"
}: CompressedVideoViewProps) {
  
  if (viewMode === "list") {
    return (
      <div className="p-3 space-y-3">
        {compressedVideos.map((video) => (
          <div
            key={video.id}
            onClick={() => console.log(video.id)}
            className={`flex gap-3 cursor-pointer p-2  ${
              false ? 'ring-2 ring-blue-400' : 'hover:bg-gray-800/50'
            }`}
          >
            {/* Video thumbnail */}
            <div className="relative w-[140px] h-[94px] overflow-hidden flex-shrink-0">
              <Image
                src={video.thumbnail}
                alt={video.title}
                fill
                className="object-cover w-[140px] h-[94px]"
              />
            </div>

            {/* Video info */}
            <div className="flex-1 min-w-0 flex flex-col justify-between">
              <h4 className="text-white font-regular text-lg tracking-[0]">
                {video.title}
              </h4>
              <div className="flex items-center gap-6 font-normal text-[15px] leading-[15px] tracking-[0] text-gray-400">
                <span>{video.size}</span>
                <span>{video.duration}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Grid view (default)
  return (
    <div className="p-3">
      <div className="grid grid-cols-2 gap-3">
        {compressedVideos.map((video) => (
          <div
            key={video.id}
            onClick={() => console.log(video.id)}
            className={`relative cursor-pointer ${
              false ? 'ring-2 ring-blue-400' : ''
            }`}
          >
            {/* Video thumbnail */}
            <div className="relative aspect-video  overflow-hidden ">
              <Image
                src={video.thumbnail}
                alt={video.title}
                fill
                className="object-cover w-36 h-24"
              />
              
              {/* Size and Duration overlay */}
              <div className="text-sm  tracking-[0]  ">
                <span className="absolute left-2 bottom-2  ">{video.size}</span>
                <span className="absolute right-2 bottom-2  ">{video.duration}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
