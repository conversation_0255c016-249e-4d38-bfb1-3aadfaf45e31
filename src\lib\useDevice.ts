// hooks/useDevice.ts
"use client";

import { useEffect, useState } from "react";

export type DeviceInfo = {
  userAgent: string;
  isMobile: boolean;
  isDesktop: boolean;
};

export function useDevice(): DeviceInfo {
  const [info, setInfo] = useState<DeviceInfo>({
    userAgent: "",
    isMobile: false,
    isDesktop: true,
  });

  useEffect(() => {
    const ua = navigator.userAgent;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      ua
    );

    setInfo({
      userAgent: ua,
      isMobile,
      isDesktop: !isMobile,
    });
  }, []);

  return info;
}
