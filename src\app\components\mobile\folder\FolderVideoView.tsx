"use client";

import Image from "next/image";

type VideoData = {
  id: number;
  title: string;
  thumbnail: string;
  size: string;
  duration: string;
};

// Data mẫu cho videos trong folder
const folderVideos: Record<string, VideoData[]> = {
  camera: [
    { id: 1, title: "Survival in the deep forest", thumbnail: "/assets/images/Video1.png", size: "32MB", duration: "00:00:25" },
    { id: 2, title: "Đại tướng <PERSON>", thumbnail: "/assets/images/Video2.png", size: "32MB", duration: "00:00:25" },
    { id: 3, title: "Nữ quái công an", thumbnail: "/assets/images/Video3.png", size: "32MB", duration: "00:00:25" },
    { id: 4, title: "Nữ Đại Úy", thumbnail: "/assets/images/Video4.png", size: "32MB", duration: "00:00:25" },
    { id: 5, title: "<PERSON><PERSON> viên hàng không", thumbnail: "/assets/images/Video5.png", size: "32MB", duration: "00:00:25" },
    { id: 6, title: "Travel Video 1", thumbnail: "/assets/images/Video6.png", size: "32MB", duration: "00:00:25" },
    { id: 7, title: "Travel Video 2", thumbnail: "/assets/images/Video7.png", size: "32MB", duration: "00:00:25" },
    { id: 8, title: "Travel Video 3", thumbnail: "/assets/images/Video8.png", size: "32MB", duration: "00:00:25" },
    { id: 9, title: "Travel Video 4", thumbnail: "/assets/images/Video9.png", size: "32MB", duration: "00:00:25" },
    { id: 10, title: "Travel Video 5", thumbnail: "/assets/images/Video10.png", size: "32MB", duration: "00:00:25" },
  ],
  "fast-convert": [
    { id: 11, title: "Fast Convert Video 1", thumbnail: "/assets/images/Video5.png", size: "32MB", duration: "00:00:25" },
    { id: 12, title: "Fast Convert Video 2", thumbnail: "/assets/images/Video6.png", size: "32MB", duration: "00:00:25" },
    { id: 13, title: "Fast Convert Video 3", thumbnail: "/assets/images/Video7.png", size: "32MB", duration: "00:00:25" },
  ],
  travel: [
    { id: 15, title: "Travel Adventure 1", thumbnail: "/assets/images/Video9.png", size: "32MB", duration: "00:00:25" },
    { id: 16, title: "Travel Adventure 2", thumbnail: "/assets/images/Video10.png", size: "32MB", duration: "00:00:25" },
    { id: 17, title: "Travel Adventure 3", thumbnail: "/assets/images/Video1.png", size: "32MB", duration: "00:00:25" },
    { id: 18, title: "Travel Adventure 4", thumbnail: "/assets/images/Video2.png", size: "32MB", duration: "00:00:25" },
  ]
};

type FolderVideoViewProps = {
  folderId: string;
  selectedVideos?: Set<number>;
  onVideoSelect?: (videoId: number) => void;
};

export default function FolderVideoView({ 
  folderId, 
  selectedVideos = new Set(), 
  onVideoSelect 
}: FolderVideoViewProps) {
  const videos = folderVideos[folderId] || [];

  return (
    <div className="p-3">
      <div className="grid grid-cols-2 gap-3">
        {videos.map((video) => (
          <div
            key={video.id}
            onClick={() => onVideoSelect?.(video.id)}
            className={`relative cursor-pointer ${selectedVideos.has(video.id) ? 'ring-2 ring-blue-400' : ''}`}
          >
            {/* Video thumbnail */}
            <div className="relative aspect-video overflow-hidden ">
              <Image
                src={video.thumbnail}
                alt={video.title}
                fill
                className="object-cover w-36 h-24"
              />
              
              {/* Size and Duration overlay */}
              <div className="text-sm  tracking-[0]  ">
                <span className="absolute left-2 bottom-2  ">{video.size}</span>
                <span className="absolute right-2 bottom-2  ">{video.duration}</span>
              </div>

            
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
