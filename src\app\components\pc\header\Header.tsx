import React from "react";
import { But<PERSON> } from "antd";
import Image from "next/image";
import HeaderMenu from "./HeaderMenu";

const HeaderPc: React.FC = () => {
  return (
    <header className="w-full bg-white shadow-sm px-8 py-3 flex items-center justify-center">
      {/* Wrapper */}
      <div className="w-content flex justify-between">
        {/* Logo */}
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 relative">
            <Image
              src="/assets/svg/fast-logo.svg"
              alt="FASTconvert Logo"
              className="h-8 w-8 object-contain"
              fill
            />
          </div>
          <span className="font-bold text-[35px]">
            <span className="text-primary">FAST</span>convert
          </span>
        </div>

        {/* Menu */}
        <HeaderMenu />

        {/* Auth Buttons */}
        <div className="flex items-center space-x-4">
          <a
            href="#"
            className="text-gray-600 hover:text-primary text-18 !font-light"
          >
            Register
          </a>
          <Button
            type="default"
            className="!px-7 !border-primary !rounded-full text-primary hover:bg-primary !text-18 hover:text-white"
          >
            Login
          </Button>
        </div>
      </div>
    </header>
  );
};

export default HeaderPc;
