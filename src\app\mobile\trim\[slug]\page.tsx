import MyVideo from "@/components/mobile/video/MyVideo";
import Image from "next/image";
import Link from "next/link";

interface Props {
  params: Promise<{ slug: string }>;
}

export default async function TrimPage({ params }: Props) {
  const { slug } = await params;

  return (
    <main className="min-h-screen bg-neutral-900 text-white flex flex-col">
      {/* Header */}
      <header className="sticky top-0 z-40 bg-gradient-to-b from-primary-2 to-primary">
        <div className="px-4 py-3 flex items-center gap-2">
          <Link
            href="/mobile"
            className="inline-flex h-8 w-8 items-center justify-center rounded-full hover:bg-white/10"
            aria-label="Back"
          >
            <Image
              src="/assets/svg/back.svg"
              alt="Back"
              width={19}
              height={19}
            />
          </Link>
          <h1 className="text-base font-semibold">Trim video</h1>
          <span className="ml-auto text-xs opacity-70">ID: {slug}</span>
        </div>
      </header>

      {/* Player area */}
      <section className="flex-1 bg-black relative">
        <div className="flex items-center justify-center w-full">
          <MyVideo
            src={[{ src: "/assets/videos/demo.mp4", type: "video/mp4" }]}
            poster="/assets/images/Video1.png"
            caption=""
            aspectClassName=""
            roundedClassName=""
            controls={true} // dùng overlay play ở giữa
            autoPlayOnClick={true}
            loop={false}
          />
        </div>
      </section>

      {/* Bottom editor panel */}
      <section className="bg-neutral-900">
        {/* Title row */}
        <div className="px-4 py-2 text-center text-sm">
          <span className="inline-block truncate max-w-[92%]">
            Hội cựu sinh viên ĐH QG
          </span>
        </div>

        {/* Timeline strip */}
        <div className="px-4 pb-2">
          <div className="relative h-16 rounded-md bg-neutral-800 overflow-hidden">
            {/* giả lập thumbnail strip */}
            <div className="absolute inset-0 grid grid-cols-8">
              {Array.from({ length: 8 }).map((_, i) => (
                <div
                  key={i}
                  className="bg-[url('/assets/images/Video1.png')] bg-cover bg-center opacity-70"
                />
              ))}
            </div>

            {/* vùng chọn + handles (chỉ UI) */}
            <div className="absolute inset-y-0 left-12 right-12 ring-2 ring-white/90 rounded-sm" />
            <div className="absolute inset-y-0 left-12 w-[6px] bg-white rounded-sm shadow" />
            <div className="absolute inset-y-0 right-12 w-[6px] bg-white rounded-sm shadow" />
          </div>

          {/* time labels */}
          <div className="mt-2 flex justify-between text-xs text-white/80">
            <span>00:00:01</span>
            <span>00:00:15</span>
          </div>
        </div>
      </section>

      {/* Footer actions */}
      <footer className="sticky bottom-0">
        <div className="flex gap-1 text-[22px]">
          <Link
            href="/mobile"
            className="flex-1 bg-primary hover:bg-primary-2 text-white text-center py-4 font-semibold tracking-wide"
          >
            CANCEL
          </Link>
          <Link
            href={`/mobile/trim/${slug}/result`}
            className="flex-1 bg-primary hover:bg-primary-2 text-white text-center py-4 font-semibold tracking-wide"
          >
            OK
          </Link>
        </div>
      </footer>
    </main>
  );
}
