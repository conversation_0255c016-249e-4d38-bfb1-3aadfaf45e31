/* CSS Module chỉ cho damage number effects và animations */

/* Animated number với scale effect */
.animatedNumber {
  animation: numberPop 0.6s ease-out;
}

@keyframes numberPop {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Critical hit effects */
.criticalHit {
  animation: criticalShake 0.8s ease-out;
}

@keyframes criticalShake {
  0%,
  100% {
    transform: translateX(0px);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

.criticalText {
  animation: criticalFlash 0.5s ease-in-out;
  color: #fbbf24;
  text-shadow: 0 0 10px #fbbf24;
}

@keyframes criticalFlash {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* Floating number animation */
.floatingNumber {
  animation: floatUp 2s ease-out forwards;
}

@keyframes floatUp {
  0% {
    transform: translateY(0px);
    opacity: 1;
  }
  100% {
    transform: translateY(-50px);
    opacity: 0;
  }
}

/* Damage value với number counting effect */
.damageValue {
  display: inline-block;
  animation: numberCount 0.5s ease-out;
}

@keyframes numberCount {
  0% {
    transform: scale(0.8);
  }
  100% {
    transform: scale(1);
  }
}

/* Type icon animation */
.typeIcon {
  display: inline-block;
  animation: iconBounce 0.8s ease-out;
}

@keyframes iconBounce {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

/* Critical burst effect */
.criticalBurst {
  position: absolute;
  inset: -10px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(251, 191, 36, 0.6) 0%,
    rgba(251, 191, 36, 0.3) 50%,
    transparent 100%
  );
  animation: burstExpand 0.6s ease-out;
  pointer-events: none;
}

@keyframes burstExpand {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}

/* Floating trail effect */
.floatingTrail {
  position: absolute;
  inset: 0;
  background: linear-gradient(to top, currentColor 0%, transparent 100%);
  opacity: 0.3;
  animation: trailFade 2s ease-out;
  pointer-events: none;
}

@keyframes trailFade {
  0% {
    opacity: 0.5;
    transform: scaleY(1);
  }
  100% {
    opacity: 0;
    transform: scaleY(0);
  }
}

/* Damage glow effects */
.damageGlow {
  position: absolute;
  inset: -5px;
  border-radius: 8px;
  opacity: 0.6;
  animation: glowPulse 1s ease-in-out;
  pointer-events: none;
}

@keyframes glowPulse {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
  100% {
    opacity: 0;
    transform: scale(1.3);
  }
}

/* Type-specific glow colors */
.glow-physical {
  background: radial-gradient(ellipse, rgba(239, 68, 68, 0.5), transparent);
}

.glow-magical {
  background: radial-gradient(ellipse, rgba(59, 130, 246, 0.5), transparent);
}

.glow-true {
  background: radial-gradient(ellipse, rgba(255, 255, 255, 0.5), transparent);
}

.glow-heal {
  background: radial-gradient(ellipse, rgba(34, 197, 94, 0.5), transparent);
}

/* Size-specific enhancements */
.size-sm .criticalBurst {
  inset: -5px;
}

.size-lg .criticalBurst {
  inset: -15px;
}

.size-xl .criticalBurst {
  inset: -20px;
}

/* Type-specific animations */
.type-physical .damageValue {
  animation: physicalImpact 0.4s ease-out;
}

@keyframes physicalImpact {
  0% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.1) rotate(-2deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

.type-magical .damageValue {
  animation: magicalSparkle 0.6s ease-out;
}

@keyframes magicalSparkle {
  0%,
  100% {
    filter: brightness(1);
  }
  50% {
    filter: brightness(1.5) hue-rotate(30deg);
  }
}

.type-heal .damageValue {
  animation: healPulse 0.8s ease-out;
}

@keyframes healPulse {
  0%,
  100% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.1);
    filter: brightness(1.3);
  }
}
