'use client';
import React from "react";
import VideoItem from "./VideoItem";
import { VideoGroup, VideoItemModel } from "./type";

type Props = {
  groups?: VideoGroup[]; // cho phép truyền từ API
  onItemClick: (item: VideoItemModel) => void;
  className?: string;
};

const mock: VideoGroup[] = [
  {
    title: "Today",
    videos: [
      {
        id: "1",
        thumbnail: "/assets/images/Video1.png",
        size: "32MB",
        duration: "00:00:25",
      },
      {
        id: "2",
        thumbnail: "/assets/images/Video2.png",
        size: "32MB",
        duration: "00:00:25",
      },
      {
        id: "3",
        thumbnail: "/assets/images/Video3.png",
        size: "32MB",
        duration: "00:00:25",
      },
      {
        id: "4",
        thumbnail: "/assets/images/Video4.png",
        size: "32MB",
        duration: "00:00:25",
      },
    ],
  },
  {
    title: "Tháng 8",
    videos: [
      {
        id: "5",
        thumbnail: "/assets/images/Video5.png",
        size: "32MB",
        duration: "00:00:25",
      },
      {
        id: "6",
        thumbnail: "/assets/images/Video6.png",
        size: "32MB",
        duration: "00:00:25",
      },
      {
        id: "7",
        thumbnail: "/assets/images/Video7.png",
        size: "32MB",
        duration: "00:00:25",
      },
      {
        id: "8",
        thumbnail: "/assets/images/Video8.png",
        size: "32MB",
        duration: "00:00:25",
      },
    ],
  },
  {
    title: "Tháng 7",
    videos: [
      {
        id: "9",
        thumbnail: "/assets/images/Video9.png",
        size: "32MB",
        duration: "00:00:25",
      },
      {
        id: "10",
        thumbnail: "/assets/images/Video10.png",
        size: "32MB",
        duration: "00:00:25",
      },
    ],
  },
];

const VideoGallery: React.FC<Props> = ({
  groups = mock,
  onItemClick,
  className,
}) => {
  return (
    <div className={`space-y-6 p-4 ${className || ""}`}>
      {groups.map((group) => (
        <section key={group.title} className="space-y-3">
          <h2 className="text-base font-semibold text-white/90">
            {group.title}
          </h2>
          <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4">
            {group.videos.map((v) => (
              <VideoItem 
                key={v.id} 
                title={v.title || `Video ${v.id}`}
                thumbnail={v.thumbnail}
                size={v.size}
                duration={v.duration}
              />
            ))}
          </div>
        </section>
      ))}
    </div>
  );
};

export default VideoGallery;
