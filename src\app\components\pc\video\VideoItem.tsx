'use client';
import React from "react";
import { VideoItemModel } from "./type";

// Nếu dùng Next.js Image, thay <img> bằng <Image> (ghi chú ở dưới)
type Props = {
  item: VideoItemModel;
  onClick: (item: VideoItemModel) => void;
  className?: string;
};

const VideoItem: React.FC<Props> = ({ item, onClick, className }) => {

  const handleClick = () => {
    onClick(item);
  };

  console.log(item.thumbnail)

  return (
    <button
      onClick={handleClick}
      className={`group relative w-full overflow-hidden rounded-xl bg-neutral-900 focus:outline-none focus:ring-2 focus:ring-violet-500 ${
        className || ""
      }`}
      aria-label={item.title || "Video item"}
    >
      {/* Thumbnail */}
      <img
        src={item.thumbnail}
        alt={item.title || "video thumbnail"}
        className="h-36 w-full object-cover transition-transform duration-300 group-hover:scale-105"
        loading="lazy"
      />
      {/* Overlay gradient */}
      <div className="pointer-events-none absolute inset-0 bg-gradient-to-t from-black/70 via-black/0 to-transparent opacity-100" />
      {/* Meta (size, duration) */}
      <div className="pointer-events-none absolute inset-x-0 bottom-0 flex items-center justify-between px-2 py-1 text-sm font-medium text-white/90">
        <span>{item.size}</span>
        <span>{item.duration}</span>
      </div>
      {/* Play icon hover */}
      <div className="pointer-events-none absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-200 group-hover:opacity-100">
        <div className="rounded-full bg-black/50 p-2">
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="text-white"
            aria-hidden
          >
            <path d="M8 5v14l11-7z" />
          </svg>
        </div>
      </div>
      {/* Optional title (nếu có) */}
      {item.title && (
        <div className="pointer-events-none absolute left-2 top-2 max-w-[85%] truncate rounded bg-black/50 px-2 py-0.5 text-xs font-semibold text-white">
          {item.title}
        </div>
      )}
    </button>
  );
};

export default VideoItem;
