{"name": "lucky", "version": "0.1.0", "private": true, "prisma": {"seed": "tsx prisma/seed.ts"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.13.0", "@prisma/extension-accelerate": "^2.0.2", "@prisma/internals": "^6.13.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.84.1", "@tanstack/react-query-devtools": "^5.84.1", "@tanstack/react-table": "^8.21.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.4.2", "next-themes": "^0.4.6", "react": "19.1.0", "react-day-picker": "^9.8.1", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-resizable-panels": "^3.0.4", "recharts": "^2.15.4", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^4.0.15", "zustand": "^5.0.7"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@eslint/eslintrc": "^3", "@storybook/addon-a11y": "^9.1.1", "@storybook/addon-docs": "^9.1.1", "@storybook/addon-vitest": "^9.1.1", "@storybook/nextjs-vite": "^9.1.1", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.2", "eslint-plugin-storybook": "^9.0.18", "playwright": "^1.54.1", "postcss": "^8.5.6", "prisma": "^6.13.0", "storybook": "^9.0.18", "tailwindcss": "^4.1.12", "ts-node": "^10.9.2", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5.9.2", "vitest": "^3.2.4"}}