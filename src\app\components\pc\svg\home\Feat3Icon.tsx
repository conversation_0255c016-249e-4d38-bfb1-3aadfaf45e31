"use client";
import React from "react";

const Feat3IconSVG: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {
  return (
    <svg
      width="67"
      height="49"
      viewBox="0 0 67 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M42.4278 0C37.8643 0.00349286 33.3918 1.27271 29.5104 3.6657C25.629 6.05869 22.4918 9.4811 20.4497 13.5502C18.0203 13.1969 15.5439 13.3465 13.1751 13.9897C10.8062 14.6329 8.59573 15.7559 6.68142 17.2886C4.76712 18.8212 3.19008 20.7308 2.04869 22.8981C0.907295 25.0653 0.226027 27.4438 0.0473767 29.8853C-0.131273 32.3267 0.196528 34.7786 1.01033 37.0881C1.82414 39.3976 3.10649 41.5151 4.77741 43.3086C6.44832 45.1021 8.47195 46.5331 10.7221 47.5123C12.9722 48.4915 15.4006 48.9979 17.8557 49H42.4278C48.9448 49 55.1948 46.4188 59.803 41.8241C64.4112 37.2295 67 30.9978 67 24.5C67 18.0022 64.4112 11.7705 59.803 7.17588C55.1948 2.58124 48.9448 0 42.4278 0ZM42.4278 44.5455H17.8557C14.301 44.5455 10.8919 43.1375 8.37831 40.6313C5.86476 38.1252 4.45266 34.7261 4.45266 31.1818C4.45266 27.6376 5.86476 24.2385 8.37831 21.7323C10.8919 19.2261 14.301 17.8182 17.8557 17.8182C18.1628 17.8182 18.47 17.8182 18.7743 17.8488C18.1634 20.0133 17.8543 22.2514 17.8557 24.5C17.8557 25.0907 18.091 25.6572 18.5099 26.0749C18.9289 26.4926 19.497 26.7273 20.0895 26.7273C20.6819 26.7273 21.2501 26.4926 21.6691 26.0749C22.088 25.6572 22.3233 25.0907 22.3233 24.5C22.3233 20.5354 23.5024 16.6598 25.7115 13.3633C27.9207 10.0669 31.0606 7.49761 34.7342 5.98042C38.4078 4.46322 42.4501 4.06626 46.35 4.83971C50.2499 5.61317 53.8322 7.52232 56.6439 10.3257C59.4555 13.1291 61.3703 16.7009 62.146 20.5893C62.9218 24.4778 62.5236 28.5082 61.002 32.1711C59.4803 35.8339 56.9035 38.9646 53.5973 41.1672C50.2911 43.3698 46.4041 44.5455 42.4278 44.5455Z"
        fill="#4E2EAF"
      />
    </svg>
  );
};

export default Feat3IconSVG;
