/* MapFarmHex - 7x7 Hexagonal Grid Layout với thuật to<PERSON> tổ ong thật sự */

/* Map container */
.mapContainer {
  animation: mapAppear 0.8s ease-out;
}

@keyframes mapAppear {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 7x7 Hex Grid - Absolute positioning v<PERSON>i thu<PERSON>t toán hexagonal */
.hexGrid {
  position: relative;
  width: 400px;
  height: 320px;
  margin: 0 auto;
}

/* Hex Cell - Positioned theo thuật toán hexagonal grid */
.hexCell {
  position: absolute;
  width: 50px;
  height: 58px; /* Tăng height để hex không bị méo */
  cursor: pointer;
  transition: all 0.3s ease;

  /* Thuật toán hexagonal grid thật sự:
     - Hex width = 50px
     - Hex height = 58px (50 * sqrt(3) * 2/3 ≈ 58)
     - Horizontal spacing = 37.5px (75% của width)
     - Vertical spacing = 43.5px (75% của height)
     - Odd rows offset = 25px (50% của width)
  */
  left: calc(var(--hex-q, 0) * 37.5px + (var(--hex-r, 0) % 2) * 25px);
  top: calc(var(--hex-r, 0) * 43.5px);
}

/* Truyền coordinates vào CSS variables */
.hexCell {
  --hex-q: attr(data-q number, 0);
  --hex-r: attr(data-r number, 0);
}

/* Animated appearance */
.hexCell.animated {
  animation: hexAppear 0.5s ease-out both;
  animation-delay: var(--animation-delay, 0s);
}

@keyframes hexAppear {
  0% {
    opacity: 0;
    transform: scale(0.3) rotate(15deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* Perfect Hexagon Shape với tỉ lệ đúng */
.hexagon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 1px solid;
  transition: all 0.3s ease;

  /* Hexagon clip-path với tỉ lệ chính xác cho tổ ong */
  clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
}

/* Cell Content */
.cellContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.emptyCell {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* Hero Effects */
.heroCell .hexagon {
  animation: heroPresence 3s ease-in-out infinite alternate;
}

@keyframes heroPresence {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

.heroContent {
  animation: heroFloat 4s ease-in-out infinite;
}

@keyframes heroFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

/* Monster Effects */
.monsterCell .hexagon {
  animation: monsterThreat 2.5s ease-in-out infinite alternate;
}

@keyframes monsterThreat {
  0% {
    filter: brightness(1);
  }
  100% {
    filter: brightness(1.1);
  }
}

.monsterContent {
  animation: monsterIdle 3s ease-in-out infinite;
}

@keyframes monsterIdle {
  0%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-1deg);
  }
  75% {
    transform: rotate(1deg);
  }
}

/* Highlighted Cell */
.highlightedCell .hexagon {
  animation: highlightPulse 2s ease-in-out infinite;
}

@keyframes highlightPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Selected Cell */
.selectedCell .hexagon {
  animation: selectedBounce 1.5s ease-in-out infinite;
}

@keyframes selectedBounce {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.15);
  }
}

/* Glow Effects */
.highlightGlow {
  position: absolute;
  inset: -4px;
  border-radius: 50%;
  background: radial-gradient(
    ellipse,
    rgba(251, 191, 36, 0.6) 0%,
    transparent 70%
  );
  animation: highlightGlowPulse 2s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes highlightGlowPulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.selectionGlow {
  position: absolute;
  inset: -6px;
  border-radius: 50%;
  background: radial-gradient(
    ellipse,
    rgba(59, 130, 246, 0.8) 0%,
    transparent 70%
  );
  animation: selectionGlowPulse 1s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes selectionGlowPulse {
  0%,
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.3);
  }
}

.heroGlow {
  position: absolute;
  inset: -3px;
  border-radius: 50%;
  background: radial-gradient(
    ellipse,
    rgba(59, 130, 246, 0.4) 0%,
    transparent 70%
  );
  animation: heroGlowPulse 3s ease-in-out infinite alternate;
  pointer-events: none;
  z-index: 1;
}

@keyframes heroGlowPulse {
  0% {
    opacity: 0.4;
  }
  100% {
    opacity: 0.7;
  }
}

.monsterGlow {
  position: absolute;
  inset: -3px;
  border-radius: 50%;
  background: radial-gradient(
    ellipse,
    rgba(239, 68, 68, 0.4) 0%,
    transparent 70%
  );
  animation: monsterGlowPulse 2s ease-in-out infinite alternate;
  pointer-events: none;
  z-index: 1;
}

@keyframes monsterGlowPulse {
  0% {
    opacity: 0.4;
  }
  100% {
    opacity: 0.8;
  }
}

/* Hover Effects */
.hexCell:hover .hexagon {
  transform: scale(1.1);
  filter: brightness(1.1);
}

.hexCell:hover .heroContent {
  animation-duration: 2s;
}

.hexCell:hover .monsterContent {
  animation-duration: 1.5s;
}

/* Responsive */
@media (max-width: 768px) {
  .hexGrid {
    width: 320px;
    height: 256px;
  }

  .hexCell {
    width: 40px;
    height: 46px;
    left: calc(var(--hex-q, 0) * 30px + (var(--hex-r, 0) % 2) * 20px);
    top: calc(var(--hex-r, 0) * 34.5px);
  }

  .hexCell.animated {
    animation: none;
  }

  .heroCell .hexagon,
  .monsterCell .hexagon,
  .highlightedCell .hexagon,
  .selectedCell .hexagon {
    animation-duration: 1s;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .mapContainer,
  .hexCell,
  .heroCell .hexagon,
  .monsterCell .hexagon,
  .highlightedCell .hexagon,
  .selectedCell .hexagon,
  .heroContent,
  .monsterContent,
  .highlightGlow,
  .selectionGlow,
  .heroGlow,
  .monsterGlow {
    animation: none;
  }
}
